/**
 * Professional Paytable System for Slot Machine Games
 * Implements industry-standard paytable management with:
 * - Configurable symbol values and weights
 * - Wild substitution logic
 * - Scatter pay calculations
 * - Bonus feature triggers
 * - RTP-based payout adjustments
 */

import { gameRNG } from './RNG';

export interface SymbolDefinition {
  id: string;
  name: string;
  type: 'wild' | 'scatter' | 'bonus' | 'high' | 'medium' | 'low';
  weight: number; // Probability weight for reel strips
  payouts: { [count: number]: number }; // count -> multiplier
  isWild?: boolean;
  isScatter?: boolean;
  canSubstitute?: string[]; // Which symbols this can substitute for
  specialFeatures?: {
    expandingWild?: boolean;
    stickyWild?: boolean;
    multiplier?: number;
    triggerBonus?: string;
  };
}

export interface PaylineDefinition {
  id: number;
  name: string;
  positions: Array<{ reel: number; row: number }>;
  active: boolean;
}

export interface WinCombination {
  paylineId: number;
  symbolId: string;
  count: number;
  positions: Array<{ reel: number; row: number }>;
  payout: number;
  multiplier: number;
  isWildWin: boolean;
  isScatterWin: boolean;
}

export interface PaytableConfig {
  symbols: SymbolDefinition[];
  paylines: PaylineDefinition[];
  baseRTP: number;
  volatility: 'low' | 'medium' | 'high';
  hitFrequency: number; // Percentage of spins that result in a win
  maxWinMultiplier: number; // Maximum win as multiplier of bet
  scatterPayMultiplier: number; // Multiplier for scatter pays
  wildMultiplier: number; // Additional multiplier when wild is involved
}

export class PaytableSystem {
  private config: PaytableConfig;
  private symbolMap: Map<string, SymbolDefinition>;
  private paylineMap: Map<number, PaylineDefinition>;
  private rtpAdjustmentFactor: number = 1.0;

  constructor(config: PaytableConfig) {
    this.config = config;
    this.symbolMap = new Map();
    this.paylineMap = new Map();
    
    // Build symbol and payline maps for quick lookup
    this.buildMaps();
    
    // Calculate RTP adjustment factor
    this.calculateRTPAdjustment();
    
    console.log('💰 PaytableSystem: Initialized with', {
      symbols: this.config.symbols.length,
      paylines: this.config.paylines.length,
      targetRTP: this.config.baseRTP,
      volatility: this.config.volatility
    });
  }

  /**
   * Build internal maps for efficient lookups
   */
  private buildMaps(): void {
    // Build symbol map
    this.config.symbols.forEach(symbol => {
      this.symbolMap.set(symbol.id, symbol);
    });

    // Build payline map
    this.config.paylines.forEach(payline => {
      this.paylineMap.set(payline.id, payline);
    });
  }

  /**
   * Calculate RTP adjustment factor based on configuration
   */
  private calculateRTPAdjustment(): void {
    // This would typically be calculated based on extensive simulation
    // For now, we'll use a simplified approach
    const baseRTP = this.config.baseRTP;
    const targetRTP = 96.0; // Industry standard
    
    this.rtpAdjustmentFactor = targetRTP / baseRTP;
    
    console.log('💰 PaytableSystem: RTP adjustment factor:', this.rtpAdjustmentFactor);
  }

  /**
   * Get symbol definition by ID
   */
  public getSymbol(symbolId: string): SymbolDefinition | undefined {
    return this.symbolMap.get(symbolId);
  }

  /**
   * Get all symbols of a specific type
   */
  public getSymbolsByType(type: SymbolDefinition['type']): SymbolDefinition[] {
    return this.config.symbols.filter(symbol => symbol.type === type);
  }

  /**
   * Get payline definition by ID
   */
  public getPayline(paylineId: number): PaylineDefinition | undefined {
    return this.paylineMap.get(paylineId);
  }

  /**
   * Get all active paylines
   */
  public getActivePaylines(): PaylineDefinition[] {
    return this.config.paylines.filter(payline => payline.active);
  }

  /**
   * Calculate payout for a specific symbol combination
   */
  public calculateSymbolPayout(
    symbolId: string, 
    count: number, 
    betPerLine: number,
    hasWild: boolean = false
  ): number {
    const symbol = this.getSymbol(symbolId);
    if (!symbol) return 0;

    // Get base payout from symbol definition
    const basePayout = symbol.payouts[count] || 0;
    if (basePayout === 0) return 0;

    // Apply bet multiplier
    let payout = basePayout * betPerLine;

    // Apply wild multiplier if applicable
    if (hasWild && this.config.wildMultiplier > 1) {
      payout *= this.config.wildMultiplier;
    }

    // Apply RTP adjustment
    payout *= this.rtpAdjustmentFactor;

    // Apply volatility adjustment
    payout = this.applyVolatilityAdjustment(payout);

    return Math.round(payout * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Apply volatility-based payout adjustment
   */
  private applyVolatilityAdjustment(payout: number): number {
    switch (this.config.volatility) {
      case 'low':
        // More frequent, smaller wins
        return payout * 0.8;
      case 'high':
        // Less frequent, larger wins
        return payout * 1.3;
      case 'medium':
      default:
        return payout;
    }
  }

  /**
   * Calculate scatter pay (independent of paylines)
   */
  public calculateScatterPay(
    symbolId: string, 
    count: number, 
    totalBet: number
  ): number {
    const symbol = this.getSymbol(symbolId);
    if (!symbol || !symbol.isScatter) return 0;

    const basePayout = symbol.payouts[count] || 0;
    if (basePayout === 0) return 0;

    // Scatter pays are typically based on total bet, not line bet
    let payout = basePayout * totalBet * this.config.scatterPayMultiplier;

    // Apply RTP adjustment
    payout *= this.rtpAdjustmentFactor;

    return Math.round(payout * 100) / 100;
  }

  /**
   * Check if a symbol can substitute for another
   */
  public canSubstitute(wildSymbolId: string, targetSymbolId: string): boolean {
    const wildSymbol = this.getSymbol(wildSymbolId);
    if (!wildSymbol || !wildSymbol.isWild) return false;

    // If no specific substitution rules, wild can substitute for any non-special symbol
    if (!wildSymbol.canSubstitute) {
      const targetSymbol = this.getSymbol(targetSymbolId);
      return targetSymbol ? !targetSymbol.isScatter && !targetSymbol.isWild : false;
    }

    return wildSymbol.canSubstitute.includes(targetSymbolId);
  }

  /**
   * Evaluate a payline for wins
   */
  public evaluatePayline(
    grid: string[][],
    paylineId: number,
    betPerLine: number
  ): WinCombination | null {
    const payline = this.getPayline(paylineId);
    if (!payline || !payline.active) return null;

    // Extract symbols along the payline
    const lineSymbols: string[] = [];
    payline.positions.forEach(pos => {
      if (grid[pos.row] && grid[pos.row][pos.reel]) {
        lineSymbols.push(grid[pos.row][pos.reel]);
      }
    });

    if (lineSymbols.length === 0) return null;

    // Find the longest matching sequence from left to right
    const firstSymbol = lineSymbols[0];
    let matchCount = 1;
    let hasWild = false;

    // Check if first symbol is wild
    const firstSymbolDef = this.getSymbol(firstSymbol);
    if (firstSymbolDef?.isWild) {
      hasWild = true;
    }

    // Count consecutive matches (including wild substitutions)
    for (let i = 1; i < lineSymbols.length; i++) {
      const currentSymbol = lineSymbols[i];
      const currentSymbolDef = this.getSymbol(currentSymbol);

      // Check if current symbol matches or can be substituted
      const isMatch = 
        currentSymbol === firstSymbol ||
        (currentSymbolDef?.isWild) ||
        (firstSymbolDef?.isWild && this.canSubstitute(firstSymbol, currentSymbol)) ||
        this.canSubstitute(currentSymbol, firstSymbol);

      if (isMatch) {
        matchCount++;
        if (currentSymbolDef?.isWild) {
          hasWild = true;
        }
      } else {
        break;
      }
    }

    // Check if we have a winning combination (minimum 3 symbols)
    if (matchCount < 3) return null;

    // Determine the paying symbol (non-wild if possible)
    let payingSymbol = firstSymbol;
    if (firstSymbolDef?.isWild && matchCount > 1) {
      // Find the first non-wild symbol in the sequence
      for (let i = 1; i < matchCount; i++) {
        const symbol = lineSymbols[i];
        const symbolDef = this.getSymbol(symbol);
        if (!symbolDef?.isWild) {
          payingSymbol = symbol;
          break;
        }
      }
    }

    // Calculate payout
    const payout = this.calculateSymbolPayout(payingSymbol, matchCount, betPerLine, hasWild);
    if (payout === 0) return null;

    // Build winning positions
    const winningPositions = payline.positions.slice(0, matchCount);

    return {
      paylineId,
      symbolId: payingSymbol,
      count: matchCount,
      positions: winningPositions,
      payout,
      multiplier: hasWild ? this.config.wildMultiplier : 1,
      isWildWin: hasWild,
      isScatterWin: false
    };
  }

  /**
   * Evaluate scatter wins across the entire grid
   */
  public evaluateScatterWins(
    grid: string[][],
    totalBet: number
  ): WinCombination[] {
    const scatterWins: WinCombination[] = [];
    const scatterSymbols = this.getSymbolsByType('scatter');

    scatterSymbols.forEach(scatterSymbol => {
      const positions: Array<{ reel: number; row: number }> = [];
      
      // Count scatter symbols across the grid
      grid.forEach((row, rowIndex) => {
        row.forEach((symbol, reelIndex) => {
          if (symbol === scatterSymbol.id) {
            positions.push({ reel: reelIndex, row: rowIndex });
          }
        });
      });

      // Check if we have enough scatters for a win
      const count = positions.length;
      if (count >= 3) {
        const payout = this.calculateScatterPay(scatterSymbol.id, count, totalBet);
        
        if (payout > 0) {
          scatterWins.push({
            paylineId: -1, // Scatter wins don't use paylines
            symbolId: scatterSymbol.id,
            count,
            positions,
            payout,
            multiplier: this.config.scatterPayMultiplier,
            isWildWin: false,
            isScatterWin: true
          });
        }
      }
    });

    return scatterWins;
  }

  /**
   * Get weighted symbol for reel generation
   */
  public getWeightedSymbol(): string {
    const totalWeight = this.config.symbols.reduce((sum, symbol) => sum + symbol.weight, 0);
    const random = gameRNG.randomFloat(0, totalWeight);
    
    let currentWeight = 0;
    for (const symbol of this.config.symbols) {
      currentWeight += symbol.weight;
      if (random <= currentWeight) {
        return symbol.id;
      }
    }
    
    // Fallback to first symbol
    return this.config.symbols[0]?.id || '';
  }

  /**
   * Generate a reel strip with proper symbol distribution
   */
  public generateReelStrip(length: number): string[] {
    const strip: string[] = [];
    
    for (let i = 0; i < length; i++) {
      strip.push(this.getWeightedSymbol());
    }
    
    return strip;
  }

  /**
   * Update paytable configuration
   */
  public updateConfig(newConfig: Partial<PaytableConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.buildMaps();
    this.calculateRTPAdjustment();
    
    console.log('💰 PaytableSystem: Configuration updated');
  }

  /**
   * Get current configuration
   */
  public getConfig(): PaytableConfig {
    return { ...this.config };
  }

  /**
   * Export paytable for display/debugging
   */
  public exportPaytable(): any {
    return {
      symbols: this.config.symbols.map(symbol => ({
        id: symbol.id,
        name: symbol.name,
        type: symbol.type,
        payouts: symbol.payouts,
        weight: symbol.weight
      })),
      paylines: this.config.paylines.length,
      rtp: this.config.baseRTP,
      volatility: this.config.volatility,
      hitFrequency: this.config.hitFrequency
    };
  }
}
