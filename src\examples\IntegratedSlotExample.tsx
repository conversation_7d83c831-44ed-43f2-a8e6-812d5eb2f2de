/**
 * Example of Fully Integrated Slot Machine with Industry-Standard Winning Mechanism
 * This shows how to properly integrate the winning mechanism with a React component
 */

import React, { useState, useEffect, useRef } from 'react';
import { WinningMechanism } from '../engine/core/WinningMechanism';
import { STANDARD_5X3_CONFIG } from '../engine/config/industryStandardConfig';

interface SlotMachineProps {
  initialBalance?: number;
  initialBet?: number;
  onBalanceChange?: (balance: number) => void;
  onWin?: (amount: number, winType: string) => void;
  onBonusTriggered?: (bonusType: string) => void;
}

export const IntegratedSlotExample: React.FC<SlotMachineProps> = ({
  initialBalance = 10000, // $100.00
  initialBet = 100, // $1.00
  onBalanceChange,
  onWin,
  onBonusTriggered
}) => {
  // Initialize winning mechanism
  const winningMechanismRef = useRef<WinningMechanism | null>(null);
  const [isSpinning, setIsSpinning] = useState(false);
  const [balance, setBalance] = useState(initialBalance);
  const [bet, setBet] = useState(initialBet);
  const [lastResult, setLastResult] = useState<any>(null);
  const [sessionStats, setSessionStats] = useState({
    totalSpins: 0,
    totalWagered: 0,
    totalWon: 0,
    currentRTP: 0,
    bonusCount: 0
  });

  // Initialize winning mechanism
  useEffect(() => {
    if (!winningMechanismRef.current) {
      const mechanism = new WinningMechanism(STANDARD_5X3_CONFIG, true);
      mechanism.setBalance(initialBalance);
      winningMechanismRef.current = mechanism;
      
      console.log('🎰 Industry-standard winning mechanism initialized');
    }
  }, [initialBalance]);

  // Update session stats
  const updateSessionStats = () => {
    if (winningMechanismRef.current) {
      const auditStats = winningMechanismRef.current.getAuditSystem().getSessionStatistics();
      setSessionStats({
        totalSpins: auditStats.totalSpins,
        totalWagered: auditStats.totalWagered,
        totalWon: auditStats.totalPaidOut,
        currentRTP: auditStats.currentRTP,
        bonusCount: auditStats.bonusCount
      });
    }
  };

  // Execute spin with industry-standard mechanism
  const handleSpin = async () => {
    if (!winningMechanismRef.current || isSpinning) return;

    const totalBet = bet * 25; // 25 lines
    if (balance < totalBet) {
      alert('Insufficient balance!');
      return;
    }

    setIsSpinning(true);

    try {
      // Execute industry-standard spin
      const result = await winningMechanismRef.current.spin({
        bet: bet,
        lines: 25,
        turbo: false
      });

      // Update local state
      setBalance(result.newBalance);
      setLastResult(result);

      // Update session statistics
      updateSessionStats();

      // Trigger callbacks
      if (onBalanceChange) {
        onBalanceChange(result.newBalance);
      }

      if (result.totalPayout > 0 && onWin) {
        onWin(result.totalPayout, result.winType);
      }

      if (result.bonusTriggered && onBonusTriggered) {
        onBonusTriggered(result.freeSpinsTriggered ? 'free-spins' : 'bonus');
      }

      console.log('🎰 Spin result:', {
        winType: result.winType,
        payout: result.totalPayout,
        newBalance: result.newBalance,
        bonusTriggered: result.bonusTriggered
      });

    } catch (error) {
      console.error('Spin error:', error);
      alert('Spin failed: ' + error);
    } finally {
      setIsSpinning(false);
    }
  };

  // Adjust bet size
  const adjustBet = (newBet: number) => {
    if (newBet >= 25 && newBet <= 500) { // $0.25 to $5.00
      setBet(newBet);
    }
  };

  // Reset session
  const resetSession = () => {
    if (winningMechanismRef.current) {
      winningMechanismRef.current.resetSession();
      winningMechanismRef.current.setBalance(initialBalance);
      setBalance(initialBalance);
      setLastResult(null);
      updateSessionStats();
    }
  };

  // Generate compliance report
  const generateReport = () => {
    if (winningMechanismRef.current) {
      const report = winningMechanismRef.current.generateComplianceReport();
      console.log('📋 Compliance Report:', report);
      
      // You could display this in a modal or download as JSON
      const reportJson = JSON.stringify(report, null, 2);
      const blob = new Blob([reportJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `compliance-report-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gradient-to-b from-purple-900 to-blue-900 text-white rounded-lg shadow-2xl">
      <h1 className="text-3xl font-bold text-center mb-6">
        🎰 Industry-Standard Slot Machine
      </h1>

      {/* Game Display */}
      <div className="bg-black p-6 rounded-lg mb-6">
        <div className="text-center mb-4">
          <h2 className="text-xl font-semibold">Slot Machine Grid</h2>
        </div>
        
        {lastResult ? (
          <div className="grid grid-cols-5 gap-2 max-w-md mx-auto">
            {lastResult.grid.map((row: string[], rowIndex: number) =>
              row.map((symbol: string, colIndex: number) => (
                <div
                  key={`${rowIndex}-${colIndex}`}
                  className="bg-gray-800 border-2 border-yellow-400 p-3 text-center font-bold text-yellow-300 rounded"
                >
                  {symbol.toUpperCase()}
                </div>
              ))
            )}
          </div>
        ) : (
          <div className="grid grid-cols-5 gap-2 max-w-md mx-auto">
            {Array.from({ length: 15 }, (_, i) => (
              <div
                key={i}
                className="bg-gray-700 border-2 border-gray-500 p-3 text-center text-gray-400 rounded"
              >
                ?
              </div>
            ))}
          </div>
        )}

        {/* Last Result Display */}
        {lastResult && (
          <div className="mt-4 text-center">
            <div className="text-2xl font-bold mb-2">
              {lastResult.winType === 'no-win' ? (
                <span className="text-gray-400">No Win</span>
              ) : (
                <span className="text-green-400">
                  WIN: ${(lastResult.totalPayout / 100).toFixed(2)}
                </span>
              )}
            </div>
            {lastResult.bonusTriggered && (
              <div className="text-yellow-400 font-bold">
                🎁 BONUS TRIGGERED!
              </div>
            )}
            {lastResult.freeSpinsTriggered && (
              <div className="text-blue-400 font-bold">
                🆓 FREE SPINS!
              </div>
            )}
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="bg-gray-800 p-4 rounded-lg mb-6">
        <div className="flex flex-wrap items-center justify-center gap-4">
          <div className="text-center">
            <label className="block text-sm font-medium mb-1">Balance</label>
            <div className="text-xl font-bold text-green-400">
              ${(balance / 100).toFixed(2)}
            </div>
          </div>

          <div className="text-center">
            <label className="block text-sm font-medium mb-1">Bet per Line</label>
            <div className="flex items-center gap-2">
              <button
                onClick={() => adjustBet(bet - 25)}
                className="px-2 py-1 bg-red-600 hover:bg-red-700 rounded"
                disabled={bet <= 25}
              >
                -
              </button>
              <span className="text-lg font-bold min-w-16 text-center">
                ${(bet / 100).toFixed(2)}
              </span>
              <button
                onClick={() => adjustBet(bet + 25)}
                className="px-2 py-1 bg-green-600 hover:bg-green-700 rounded"
                disabled={bet >= 500}
              >
                +
              </button>
            </div>
          </div>

          <div className="text-center">
            <label className="block text-sm font-medium mb-1">Total Bet</label>
            <div className="text-lg font-bold text-yellow-400">
              ${((bet * 25) / 100).toFixed(2)}
            </div>
          </div>

          <button
            onClick={handleSpin}
            disabled={isSpinning || balance < bet * 25}
            className={`px-8 py-3 text-xl font-bold rounded-lg ${
              isSpinning || balance < bet * 25
                ? 'bg-gray-600 cursor-not-allowed'
                : 'bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600'
            }`}
          >
            {isSpinning ? '🎰 SPINNING...' : '🎰 SPIN'}
          </button>
        </div>
      </div>

      {/* Session Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-gray-800 p-3 rounded text-center">
          <div className="text-sm text-gray-400">Spins</div>
          <div className="text-lg font-bold">{sessionStats.totalSpins}</div>
        </div>
        <div className="bg-gray-800 p-3 rounded text-center">
          <div className="text-sm text-gray-400">Wagered</div>
          <div className="text-lg font-bold">${(sessionStats.totalWagered / 100).toFixed(0)}</div>
        </div>
        <div className="bg-gray-800 p-3 rounded text-center">
          <div className="text-sm text-gray-400">Won</div>
          <div className="text-lg font-bold">${(sessionStats.totalWon / 100).toFixed(0)}</div>
        </div>
        <div className="bg-gray-800 p-3 rounded text-center">
          <div className="text-sm text-gray-400">RTP</div>
          <div className="text-lg font-bold">{sessionStats.currentRTP.toFixed(1)}%</div>
        </div>
        <div className="bg-gray-800 p-3 rounded text-center">
          <div className="text-sm text-gray-400">Bonuses</div>
          <div className="text-lg font-bold">{sessionStats.bonusCount}</div>
        </div>
      </div>

      {/* Admin Controls */}
      <div className="flex flex-wrap justify-center gap-4">
        <button
          onClick={resetSession}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded font-semibold"
        >
          🔄 Reset Session
        </button>
        <button
          onClick={generateReport}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded font-semibold"
        >
          📋 Compliance Report
        </button>
        <button
          onClick={() => {
            if (winningMechanismRef.current) {
              const data = winningMechanismRef.current.exportAuditData();
              console.log('🔍 Audit Data:', data);
            }
          }}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded font-semibold"
        >
          🔍 Export Data
        </button>
      </div>

      {/* Industry Standards Notice */}
      <div className="mt-6 text-center text-sm text-gray-300">
        <p>
          ⚡ Powered by Industry-Standard Winning Mechanism
        </p>
        <p>
          🎯 Target RTP: {STANDARD_5X3_CONFIG.rtp.targetRTP}% | 
          🎲 Certified RNG | 
          📋 Full Audit Trail | 
          🏛️ Regulatory Compliant
        </p>
      </div>
    </div>
  );
};

export default IntegratedSlotExample;
