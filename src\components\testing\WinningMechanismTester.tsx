/**
 * React Component for Testing the Industry-Standard Winning Mechanism
 * Provides a UI to test and monitor the winning mechanism in real-time
 */

import React, { useState, useEffect, useRef } from 'react';
import { WinningMechanism } from '../../engine/core/WinningMechanism';
import { STANDARD_5X3_CONFIG } from '../../engine/config/industryStandardConfig';

interface TestResults {
  totalSpins: number;
  totalWagered: number;
  totalPaidOut: number;
  currentRTP: number;
  hitFrequency: number;
  bonusFrequency: number;
  maxWin: number;
  lastWin: number;
  complianceStatus: string;
}

export const WinningMechanismTester: React.FC = () => {
  const [winningMechanism] = useState(() => {
    const mechanism = new WinningMechanism(STANDARD_5X3_CONFIG, true);
    mechanism.setBalance(100000); // $1000.00
    return mechanism;
  });

  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResults>({
    totalSpins: 0,
    totalWagered: 0,
    totalPaidOut: 0,
    currentRTP: 0,
    hitFrequency: 0,
    bonusFrequency: 0,
    maxWin: 0,
    lastWin: 0,
    complianceStatus: 'UNKNOWN'
  });
  const [lastSpinResult, setLastSpinResult] = useState<any>(null);
  const [testSpeed, setTestSpeed] = useState(100); // ms between spins
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update results from audit system
  const updateResults = () => {
    const auditStats = winningMechanism.getAuditSystem().getSessionStatistics();
    const complianceReport = winningMechanism.generateComplianceReport();
    
    setResults({
      totalSpins: auditStats.totalSpins,
      totalWagered: auditStats.totalWagered,
      totalPaidOut: auditStats.totalPaidOut,
      currentRTP: auditStats.currentRTP,
      hitFrequency: auditStats.totalSpins > 0 ? 
        ((auditStats.totalSpins - (auditStats.totalSpins - auditStats.totalPaidOut > 0 ? 1 : 0)) / auditStats.totalSpins) * 100 : 0,
      bonusFrequency: auditStats.bonusFrequency,
      maxWin: auditStats.maxWin,
      lastWin: lastSpinResult?.totalPayout || 0,
      complianceStatus: complianceReport.complianceStatus
    });
  };

  // Execute a single spin
  const executeSpin = async () => {
    try {
      // Reset balance if getting low
      if (winningMechanism.getBalance() < 2500) {
        winningMechanism.setBalance(100000);
      }

      const result = await winningMechanism.spin({
        bet: 100, // $1.00
        lines: 25,
        turbo: false
      });

      setLastSpinResult(result);
      updateResults();
    } catch (error) {
      console.error('Spin error:', error);
      setIsRunning(false);
    }
  };

  // Start/stop automated testing
  const toggleTesting = () => {
    if (isRunning) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsRunning(false);
    } else {
      setIsRunning(true);
      intervalRef.current = setInterval(executeSpin, testSpeed);
    }
  };

  // Reset the test
  const resetTest = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsRunning(false);
    winningMechanism.resetSession();
    winningMechanism.setBalance(100000);
    setLastSpinResult(null);
    updateResults();
  };

  // Update interval when speed changes
  useEffect(() => {
    if (isRunning && intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = setInterval(executeSpin, testSpeed);
    }
  }, [testSpeed, isRunning]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PASS': return 'text-green-600';
      case 'WARNING': return 'text-yellow-600';
      case 'FAIL': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="p-6 max-w-6xl mx-auto bg-white rounded-lg shadow-lg">
      <h1 className="text-3xl font-bold text-center mb-6 text-gray-800">
        🎰 Industry-Standard Winning Mechanism Tester
      </h1>

      {/* Control Panel */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <button
            onClick={toggleTesting}
            className={`px-6 py-2 rounded-lg font-semibold ${
              isRunning 
                ? 'bg-red-500 hover:bg-red-600 text-white' 
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            {isRunning ? '⏹️ Stop Testing' : '▶️ Start Testing'}
          </button>

          <button
            onClick={executeSpin}
            disabled={isRunning}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-semibold disabled:opacity-50"
          >
            🎲 Single Spin
          </button>

          <button
            onClick={resetTest}
            className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-semibold"
          >
            🔄 Reset
          </button>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Speed:</label>
            <select
              value={testSpeed}
              onChange={(e) => setTestSpeed(Number(e.target.value))}
              className="px-2 py-1 border rounded"
            >
              <option value={50}>Very Fast (50ms)</option>
              <option value={100}>Fast (100ms)</option>
              <option value={250}>Medium (250ms)</option>
              <option value={500}>Slow (500ms)</option>
              <option value={1000}>Very Slow (1s)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Statistics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800">Total Spins</h3>
          <p className="text-2xl font-bold text-blue-600">{results.totalSpins.toLocaleString()}</p>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800">Current RTP</h3>
          <p className="text-2xl font-bold text-green-600">{results.currentRTP.toFixed(2)}%</p>
          <p className="text-sm text-green-700">Target: {STANDARD_5X3_CONFIG.rtp.targetRTP}%</p>
        </div>

        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-purple-800">Hit Frequency</h3>
          <p className="text-2xl font-bold text-purple-600">{results.hitFrequency.toFixed(1)}%</p>
        </div>

        <div className="bg-orange-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-orange-800">Compliance</h3>
          <p className={`text-2xl font-bold ${getStatusColor(results.complianceStatus)}`}>
            {results.complianceStatus}
          </p>
        </div>
      </div>

      {/* Financial Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800">Total Wagered</h3>
          <p className="text-xl font-bold text-gray-600">
            ${(results.totalWagered / 100).toFixed(2)}
          </p>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800">Total Paid Out</h3>
          <p className="text-xl font-bold text-gray-600">
            ${(results.totalPaidOut / 100).toFixed(2)}
          </p>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800">House Edge</h3>
          <p className="text-xl font-bold text-gray-600">
            {(100 - results.currentRTP).toFixed(2)}%
          </p>
        </div>
      </div>

      {/* Bonus Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-800">Bonus Frequency</h3>
          <p className="text-xl font-bold text-yellow-600">{results.bonusFrequency.toFixed(2)}%</p>
        </div>

        <div className="bg-red-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-red-800">Max Win</h3>
          <p className="text-xl font-bold text-red-600">${(results.maxWin / 100).toFixed(2)}</p>
        </div>

        <div className="bg-indigo-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-indigo-800">Last Win</h3>
          <p className="text-xl font-bold text-indigo-600">${(results.lastWin / 100).toFixed(2)}</p>
        </div>
      </div>

      {/* Last Spin Result */}
      {lastSpinResult && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Last Spin Result</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-700">Grid:</h4>
              <div className="font-mono text-sm bg-white p-2 rounded border">
                {lastSpinResult.grid.map((row: string[], i: number) => (
                  <div key={i}>{row.join(' | ')}</div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Details:</h4>
              <div className="text-sm space-y-1">
                <p><strong>Win Type:</strong> {lastSpinResult.winType}</p>
                <p><strong>Payout:</strong> ${(lastSpinResult.totalPayout / 100).toFixed(2)}</p>
                <p><strong>Wins:</strong> {lastSpinResult.wins.length}</p>
                <p><strong>Bonus:</strong> {lastSpinResult.bonusTriggered ? '🎁 Yes' : 'No'}</p>
                <p><strong>Free Spins:</strong> {lastSpinResult.freeSpinsTriggered ? '🆓 Yes' : 'No'}</p>
                <p><strong>Balance:</strong> ${(lastSpinResult.newBalance / 100).toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">How to Use This Tester</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>Single Spin:</strong> Execute one spin to see detailed results</li>
          <li>• <strong>Start Testing:</strong> Run continuous spins to test RTP over time</li>
          <li>• <strong>Speed Control:</strong> Adjust how fast spins execute during testing</li>
          <li>• <strong>Reset:</strong> Clear all data and start fresh</li>
          <li>• <strong>RTP Target:</strong> The system should converge to {STANDARD_5X3_CONFIG.rtp.targetRTP}% over many spins</li>
          <li>• <strong>Compliance:</strong> Monitor for PASS/WARNING/FAIL status based on industry standards</li>
        </ul>
      </div>
    </div>
  );
};

export default WinningMechanismTester;
