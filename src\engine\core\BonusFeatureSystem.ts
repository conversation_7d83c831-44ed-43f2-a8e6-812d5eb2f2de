/**
 * Bonus Feature System for Slot Machine Games
 * Implements industry-standard bonus features including:
 * - Free Spins with multipliers
 * - Pick-and-Click bonus games
 * - Expanding and sticky wilds
 * - Progressive features
 * - Cascading wins
 */

import { gameRNG } from './RNG';
import { PaytableSystem } from './PaytableSystem';

export interface BonusFeatureConfig {
  type: 'free-spins' | 'pick-bonus' | 'wheel-bonus' | 'expanding-wilds' | 'cascading-wins';
  triggerSymbols: string[];
  minTriggerCount: number;
  baseAward: number;
  multiplierRange: { min: number; max: number };
  rtpContribution: number; // Percentage of total RTP this feature contributes
  frequency: number; // How often this feature triggers (per 1000 spins)
}

export interface FreeSpinsConfig extends BonusFeatureConfig {
  type: 'free-spins';
  spinsAward: { [triggerCount: number]: number };
  retriggerPossible: boolean;
  multiplierProgression?: number[]; // Multiplier for each spin
  wildUpgrade?: boolean; // Whether symbols upgrade to wilds during free spins
}

export interface PickBonusConfig extends BonusFeatureConfig {
  type: 'pick-bonus';
  pickCount: number;
  prizes: Array<{
    type: 'credit' | 'multiplier' | 'extra-pick' | 'collect';
    value: number;
    weight: number;
  }>;
  collectSymbolWeight: number;
}

export interface BonusGameState {
  type: string;
  active: boolean;
  spinsRemaining?: number;
  multiplier: number;
  totalWin: number;
  picksRemaining?: number;
  collectedPrizes?: Array<{ type: string; value: number }>;
  retriggerCount: number;
  specialFeatures: string[];
}

export interface BonusResult {
  triggered: boolean;
  featureType?: string;
  award: number;
  spinsAwarded?: number;
  multiplier: number;
  newGameState?: BonusGameState;
  rtpImpact: number;
}

export class BonusFeatureSystem {
  private features: Map<string, BonusFeatureConfig>;
  private paytableSystem: PaytableSystem;
  private currentBonusState: BonusGameState | null = null;
  private debugMode: boolean;

  constructor(paytableSystem: PaytableSystem, debugMode: boolean = false) {
    this.paytableSystem = paytableSystem;
    this.debugMode = debugMode;
    this.features = new Map();
    
    // Initialize default bonus features
    this.initializeDefaultFeatures();
    
    console.log('🎁 BonusFeatureSystem: Initialized with default features');
  }

  /**
   * Initialize industry-standard bonus features
   */
  private initializeDefaultFeatures(): void {
    // Free Spins Feature
    const freeSpinsFeature: FreeSpinsConfig = {
      type: 'free-spins',
      triggerSymbols: ['scatter'],
      minTriggerCount: 3,
      baseAward: 0,
      multiplierRange: { min: 1, max: 5 },
      rtpContribution: 15, // 15% of total RTP
      frequency: 30, // Triggers 30 times per 1000 spins
      spinsAward: { 3: 10, 4: 15, 5: 20 },
      retriggerPossible: true,
      multiplierProgression: [1, 1, 2, 2, 3, 3, 4, 4, 5, 5], // Increasing multipliers
      wildUpgrade: false
    };

    // Pick Bonus Feature
    const pickBonusFeature: PickBonusConfig = {
      type: 'pick-bonus',
      triggerSymbols: ['bonus'],
      minTriggerCount: 3,
      baseAward: 0,
      multiplierRange: { min: 1, max: 10 },
      rtpContribution: 10, // 10% of total RTP
      frequency: 20, // Triggers 20 times per 1000 spins
      pickCount: 3,
      prizes: [
        { type: 'credit', value: 5, weight: 30 },
        { type: 'credit', value: 10, weight: 25 },
        { type: 'credit', value: 25, weight: 15 },
        { type: 'credit', value: 50, weight: 10 },
        { type: 'multiplier', value: 2, weight: 8 },
        { type: 'multiplier', value: 3, weight: 5 },
        { type: 'extra-pick', value: 1, weight: 5 },
        { type: 'collect', value: 0, weight: 2 }
      ],
      collectSymbolWeight: 2
    };

    this.features.set('free-spins', freeSpinsFeature);
    this.features.set('pick-bonus', pickBonusFeature);
  }

  /**
   * Check if any bonus features should trigger based on grid symbols
   */
  public checkBonusTriggers(
    grid: string[][],
    totalBet: number
  ): BonusResult {
    // If already in bonus, check for retriggers
    if (this.currentBonusState?.active) {
      return this.checkRetrigger(grid, totalBet);
    }

    // Check each feature for triggers
    for (const [featureId, feature] of this.features) {
      const triggerResult = this.checkFeatureTrigger(grid, feature, totalBet);
      if (triggerResult.triggered) {
        this.activateBonus(featureId, triggerResult);
        return triggerResult;
      }
    }

    return { triggered: false, award: 0, multiplier: 1, rtpImpact: 0 };
  }

  /**
   * Check if a specific feature should trigger
   */
  private checkFeatureTrigger(
    grid: string[][],
    feature: BonusFeatureConfig,
    totalBet: number
  ): BonusResult {
    // Count trigger symbols
    let triggerCount = 0;
    const triggerPositions: Array<{ reel: number; row: number }> = [];

    grid.forEach((row, rowIndex) => {
      row.forEach((symbol, reelIndex) => {
        if (feature.triggerSymbols.includes(symbol)) {
          triggerCount++;
          triggerPositions.push({ reel: reelIndex, row: rowIndex });
        }
      });
    });

    // Check if minimum trigger count is met
    if (triggerCount < feature.minTriggerCount) {
      return { triggered: false, award: 0, multiplier: 1, rtpImpact: 0 };
    }

    // Calculate bonus award based on feature type
    return this.calculateBonusAward(feature, triggerCount, totalBet);
  }

  /**
   * Calculate bonus award for triggered feature
   */
  private calculateBonusAward(
    feature: BonusFeatureConfig,
    triggerCount: number,
    totalBet: number
  ): BonusResult {
    switch (feature.type) {
      case 'free-spins':
        return this.calculateFreeSpinsAward(feature as FreeSpinsConfig, triggerCount, totalBet);
      case 'pick-bonus':
        return this.calculatePickBonusAward(feature as PickBonusConfig, triggerCount, totalBet);
      default:
        return { triggered: false, award: 0, multiplier: 1, rtpImpact: 0 };
    }
  }

  /**
   * Calculate free spins bonus award
   */
  private calculateFreeSpinsAward(
    feature: FreeSpinsConfig,
    triggerCount: number,
    totalBet: number
  ): BonusResult {
    const spinsAwarded = feature.spinsAward[triggerCount] || feature.spinsAward[3];
    const multiplier = gameRNG.randomInt(feature.multiplierRange.min, feature.multiplierRange.max);
    
    // Base award for triggering (some games give instant credit)
    const baseAward = feature.baseAward * totalBet;
    
    return {
      triggered: true,
      featureType: 'free-spins',
      award: baseAward,
      spinsAwarded,
      multiplier,
      rtpImpact: feature.rtpContribution,
      newGameState: {
        type: 'free-spins',
        active: true,
        spinsRemaining: spinsAwarded,
        multiplier,
        totalWin: baseAward,
        retriggerCount: 0,
        specialFeatures: feature.wildUpgrade ? ['wild-upgrade'] : []
      }
    };
  }

  /**
   * Calculate pick bonus award
   */
  private calculatePickBonusAward(
    feature: PickBonusConfig,
    triggerCount: number,
    totalBet: number
  ): BonusResult {
    const multiplier = gameRNG.randomInt(feature.multiplierRange.min, feature.multiplierRange.max);
    
    return {
      triggered: true,
      featureType: 'pick-bonus',
      award: 0, // Award calculated during pick game
      multiplier,
      rtpImpact: feature.rtpContribution,
      newGameState: {
        type: 'pick-bonus',
        active: true,
        multiplier,
        totalWin: 0,
        picksRemaining: feature.pickCount,
        collectedPrizes: [],
        retriggerCount: 0,
        specialFeatures: []
      }
    };
  }

  /**
   * Check for retrigger during active bonus
   */
  private checkRetrigger(grid: string[][], totalBet: number): BonusResult {
    if (!this.currentBonusState || this.currentBonusState.type !== 'free-spins') {
      return { triggered: false, award: 0, multiplier: 1, rtpImpact: 0 };
    }

    const freeSpinsFeature = this.features.get('free-spins') as FreeSpinsConfig;
    if (!freeSpinsFeature?.retriggerPossible) {
      return { triggered: false, award: 0, multiplier: 1, rtpImpact: 0 };
    }

    const triggerResult = this.checkFeatureTrigger(grid, freeSpinsFeature, totalBet);
    if (triggerResult.triggered && triggerResult.spinsAwarded) {
      // Add spins to current bonus
      this.currentBonusState.spinsRemaining! += triggerResult.spinsAwarded;
      this.currentBonusState.retriggerCount++;
      
      if (this.debugMode) {
        console.log('🎁 Free spins retriggered:', {
          spinsAdded: triggerResult.spinsAwarded,
          totalRemaining: this.currentBonusState.spinsRemaining,
          retriggerCount: this.currentBonusState.retriggerCount
        });
      }
    }

    return triggerResult;
  }

  /**
   * Activate bonus feature
   */
  private activateBonus(featureId: string, bonusResult: BonusResult): void {
    if (bonusResult.newGameState) {
      this.currentBonusState = bonusResult.newGameState;
      
      if (this.debugMode) {
        console.log('🎁 Bonus activated:', {
          featureId,
          type: this.currentBonusState.type,
          spinsRemaining: this.currentBonusState.spinsRemaining,
          multiplier: this.currentBonusState.multiplier
        });
      }
    }
  }

  /**
   * Process a spin during free spins bonus
   */
  public processFreeSpinsSpin(
    grid: string[][],
    baseWin: number,
    spinIndex: number
  ): { win: number; multiplier: number; completed: boolean } {
    if (!this.currentBonusState || this.currentBonusState.type !== 'free-spins') {
      return { win: baseWin, multiplier: 1, completed: false };
    }

    const freeSpinsFeature = this.features.get('free-spins') as FreeSpinsConfig;
    let multiplier = this.currentBonusState.multiplier;

    // Apply progressive multiplier if configured
    if (freeSpinsFeature.multiplierProgression) {
      const progressIndex = Math.min(spinIndex, freeSpinsFeature.multiplierProgression.length - 1);
      multiplier = freeSpinsFeature.multiplierProgression[progressIndex];
    }

    // Apply wild upgrade if configured
    if (freeSpinsFeature.wildUpgrade) {
      // This would modify the grid to upgrade certain symbols to wilds
      this.applyWildUpgrade(grid);
    }

    const enhancedWin = baseWin * multiplier;
    this.currentBonusState.totalWin += enhancedWin;
    this.currentBonusState.spinsRemaining!--;

    const completed = this.currentBonusState.spinsRemaining! <= 0;
    if (completed) {
      this.currentBonusState.active = false;
    }

    return { win: enhancedWin, multiplier, completed };
  }

  /**
   * Apply wild upgrade feature
   */
  private applyWildUpgrade(grid: string[][]): void {
    // Example: Convert medium symbols to wilds with 30% chance
    const upgradeChance = 0.3;
    const mediumSymbols = this.paytableSystem.getSymbolsByType('medium');
    
    grid.forEach((row, rowIndex) => {
      row.forEach((symbol, reelIndex) => {
        if (mediumSymbols.some(s => s.id === symbol) && gameRNG.random() < upgradeChance) {
          grid[rowIndex][reelIndex] = 'wild';
        }
      });
    });
  }

  /**
   * Process pick bonus game
   */
  public processPickBonusPick(pickIndex: number, totalBet: number): {
    prize: { type: string; value: number };
    totalWin: number;
    completed: boolean;
  } {
    if (!this.currentBonusState || this.currentBonusState.type !== 'pick-bonus') {
      throw new Error('No active pick bonus game');
    }

    const pickBonusFeature = this.features.get('pick-bonus') as PickBonusConfig;
    
    // Select random prize based on weights
    const totalWeight = pickBonusFeature.prizes.reduce((sum, prize) => sum + prize.weight, 0);
    const random = gameRNG.randomFloat(0, totalWeight);
    
    let currentWeight = 0;
    let selectedPrize = pickBonusFeature.prizes[0];
    
    for (const prize of pickBonusFeature.prizes) {
      currentWeight += prize.weight;
      if (random <= currentWeight) {
        selectedPrize = prize;
        break;
      }
    }

    // Process the selected prize
    let prizeValue = selectedPrize.value;
    if (selectedPrize.type === 'credit') {
      prizeValue *= totalBet;
      this.currentBonusState.totalWin += prizeValue;
    } else if (selectedPrize.type === 'multiplier') {
      this.currentBonusState.multiplier *= prizeValue;
    } else if (selectedPrize.type === 'extra-pick') {
      this.currentBonusState.picksRemaining! += prizeValue;
    }

    this.currentBonusState.collectedPrizes!.push({
      type: selectedPrize.type,
      value: prizeValue
    });

    this.currentBonusState.picksRemaining!--;
    
    const completed = selectedPrize.type === 'collect' || this.currentBonusState.picksRemaining! <= 0;
    if (completed) {
      // Apply final multiplier to total win
      this.currentBonusState.totalWin *= this.currentBonusState.multiplier;
      this.currentBonusState.active = false;
    }

    return {
      prize: { type: selectedPrize.type, value: prizeValue },
      totalWin: this.currentBonusState.totalWin,
      completed
    };
  }

  /**
   * Get current bonus state
   */
  public getCurrentBonusState(): BonusGameState | null {
    return this.currentBonusState ? { ...this.currentBonusState } : null;
  }

  /**
   * Check if bonus is active
   */
  public isBonusActive(): boolean {
    return this.currentBonusState?.active || false;
  }

  /**
   * End current bonus (for testing or forced completion)
   */
  public endCurrentBonus(): number {
    if (!this.currentBonusState) return 0;
    
    const totalWin = this.currentBonusState.totalWin;
    this.currentBonusState.active = false;
    this.currentBonusState = null;
    
    return totalWin;
  }

  /**
   * Add custom bonus feature
   */
  public addFeature(featureId: string, feature: BonusFeatureConfig): void {
    this.features.set(featureId, feature);
    console.log('🎁 BonusFeatureSystem: Added custom feature:', featureId);
  }

  /**
   * Get feature configuration
   */
  public getFeature(featureId: string): BonusFeatureConfig | undefined {
    return this.features.get(featureId);
  }

  /**
   * Calculate total RTP contribution from all bonus features
   */
  public getTotalRTPContribution(): number {
    let total = 0;
    for (const feature of this.features.values()) {
      total += feature.rtpContribution;
    }
    return total;
  }

  /**
   * Export bonus feature data for analysis
   */
  public exportFeatureData(): any {
    return {
      features: Array.from(this.features.entries()).map(([id, feature]) => ({
        id,
        type: feature.type,
        rtpContribution: feature.rtpContribution,
        frequency: feature.frequency,
        triggerSymbols: feature.triggerSymbols,
        minTriggerCount: feature.minTriggerCount
      })),
      currentBonusState: this.currentBonusState,
      totalRTPContribution: this.getTotalRTPContribution()
    };
  }
}
