/**
 * RTP (Return to Player) and Volatility Controller
 * Manages and maintains target RTP percentages with configurable volatility levels
 * Implements industry-standard RTP monitoring and adjustment mechanisms
 */

import { gameRNG } from './RNG';

export interface RTPConfig {
  targetRTP: number; // Target RTP percentage (e.g., 96.0)
  volatility: 'low' | 'medium' | 'high';
  hitFrequency: number; // Percentage of spins that should result in wins
  maxWinMultiplier: number; // Maximum win as multiplier of bet
  adjustmentWindow: number; // Number of spins to consider for RTP adjustment
  toleranceRange: number; // Acceptable deviation from target RTP (e.g., 2.0 for ±2%)
}

export interface VolatilityProfile {
  name: string;
  hitFrequency: number;
  averageWinMultiplier: number;
  maxWinMultiplier: number;
  winDistribution: { [range: string]: number }; // Percentage of wins in each range
  bonusFrequency: number; // How often bonus features trigger
}

export interface RTPMetrics {
  currentRTP: number;
  totalSpins: number;
  totalWagered: number;
  totalPaidOut: number;
  hitFrequency: number;
  averageWin: number;
  maxWin: number;
  lastAdjustment: number;
  adjustmentFactor: number;
  volatilityScore: number;
}

export interface SpinOutcome {
  wagered: number;
  payout: number;
  isWin: boolean;
  winMultiplier: number;
  bonusTriggered: boolean;
  timestamp: number;
}

export class RTPController {
  private config: RTPConfig;
  private metrics: RTPMetrics;
  private spinHistory: SpinOutcome[] = [];
  private volatilityProfiles: Map<string, VolatilityProfile>;
  private adjustmentFactor: number = 1.0;
  private debugMode: boolean;

  constructor(config: RTPConfig, debugMode: boolean = false) {
    this.config = config;
    this.debugMode = debugMode;
    
    // Initialize metrics
    this.metrics = {
      currentRTP: 0,
      totalSpins: 0,
      totalWagered: 0,
      totalPaidOut: 0,
      hitFrequency: 0,
      averageWin: 0,
      maxWin: 0,
      lastAdjustment: Date.now(),
      adjustmentFactor: 1.0,
      volatilityScore: 0
    };

    // Initialize volatility profiles
    this.initializeVolatilityProfiles();

    console.log('📊 RTPController: Initialized with target RTP:', config.targetRTP + '%');
  }

  /**
   * Initialize volatility profiles with industry-standard settings
   */
  private initializeVolatilityProfiles(): void {
    this.volatilityProfiles = new Map();

    // Low Volatility Profile
    this.volatilityProfiles.set('low', {
      name: 'Low Volatility',
      hitFrequency: 35, // 35% of spins result in wins
      averageWinMultiplier: 2.5,
      maxWinMultiplier: 50,
      winDistribution: {
        '1x-2x': 60,    // 60% of wins are 1x-2x bet
        '2x-5x': 25,    // 25% of wins are 2x-5x bet
        '5x-10x': 10,   // 10% of wins are 5x-10x bet
        '10x+': 5       // 5% of wins are 10x+ bet
      },
      bonusFrequency: 2 // 2% chance of bonus features
    });

    // Medium Volatility Profile
    this.volatilityProfiles.set('medium', {
      name: 'Medium Volatility',
      hitFrequency: 25, // 25% of spins result in wins
      averageWinMultiplier: 4.0,
      maxWinMultiplier: 200,
      winDistribution: {
        '1x-2x': 40,    // 40% of wins are 1x-2x bet
        '2x-5x': 30,    // 30% of wins are 2x-5x bet
        '5x-10x': 20,   // 20% of wins are 5x-10x bet
        '10x+': 10      // 10% of wins are 10x+ bet
      },
      bonusFrequency: 3 // 3% chance of bonus features
    });

    // High Volatility Profile
    this.volatilityProfiles.set('high', {
      name: 'High Volatility',
      hitFrequency: 15, // 15% of spins result in wins
      averageWinMultiplier: 8.0,
      maxWinMultiplier: 1000,
      winDistribution: {
        '1x-2x': 20,    // 20% of wins are 1x-2x bet
        '2x-5x': 25,    // 25% of wins are 2x-5x bet
        '5x-10x': 30,   // 30% of wins are 5x-10x bet
        '10x+': 25      // 25% of wins are 10x+ bet
      },
      bonusFrequency: 5 // 5% chance of bonus features
    });
  }

  /**
   * Record a spin outcome for RTP tracking
   */
  public recordSpin(outcome: SpinOutcome): void {
    // Add to spin history
    this.spinHistory.push(outcome);

    // Update metrics
    this.metrics.totalSpins++;
    this.metrics.totalWagered += outcome.wagered;
    this.metrics.totalPaidOut += outcome.payout;

    // Calculate current RTP
    this.metrics.currentRTP = this.metrics.totalWagered > 0 
      ? (this.metrics.totalPaidOut / this.metrics.totalWagered) * 100 
      : 0;

    // Update hit frequency
    const recentWins = this.spinHistory.slice(-1000).filter(spin => spin.isWin).length;
    const recentSpins = Math.min(this.spinHistory.length, 1000);
    this.metrics.hitFrequency = recentSpins > 0 ? (recentWins / recentSpins) * 100 : 0;

    // Update average and max win
    const wins = this.spinHistory.filter(spin => spin.isWin);
    if (wins.length > 0) {
      this.metrics.averageWin = wins.reduce((sum, spin) => sum + spin.payout, 0) / wins.length;
      this.metrics.maxWin = Math.max(...wins.map(spin => spin.payout));
    }

    // Calculate volatility score
    this.calculateVolatilityScore();

    // Trim history if it gets too large
    if (this.spinHistory.length > this.config.adjustmentWindow * 2) {
      this.spinHistory = this.spinHistory.slice(-this.config.adjustmentWindow);
    }

    // Check if adjustment is needed
    this.checkForAdjustment();

    if (this.debugMode && this.metrics.totalSpins % 100 === 0) {
      console.log('📊 RTP Metrics (every 100 spins):', {
        currentRTP: this.metrics.currentRTP.toFixed(2) + '%',
        targetRTP: this.config.targetRTP + '%',
        hitFrequency: this.metrics.hitFrequency.toFixed(1) + '%',
        totalSpins: this.metrics.totalSpins,
        adjustmentFactor: this.adjustmentFactor.toFixed(3)
      });
    }
  }

  /**
   * Calculate volatility score based on win distribution
   */
  private calculateVolatilityScore(): void {
    if (this.spinHistory.length < 100) {
      this.metrics.volatilityScore = 0;
      return;
    }

    const recentSpins = this.spinHistory.slice(-1000);
    const wins = recentSpins.filter(spin => spin.isWin);
    
    if (wins.length === 0) {
      this.metrics.volatilityScore = 0;
      return;
    }

    // Calculate variance of win multipliers
    const winMultipliers = wins.map(spin => spin.winMultiplier);
    const mean = winMultipliers.reduce((sum, mult) => sum + mult, 0) / winMultipliers.length;
    const variance = winMultipliers.reduce((sum, mult) => sum + Math.pow(mult - mean, 2), 0) / winMultipliers.length;
    
    // Normalize volatility score (0-10 scale)
    this.metrics.volatilityScore = Math.min(Math.sqrt(variance), 10);
  }

  /**
   * Check if RTP adjustment is needed
   */
  private checkForAdjustment(): void {
    // Only adjust after minimum number of spins
    if (this.metrics.totalSpins < this.config.adjustmentWindow) {
      return;
    }

    const rtpDeviation = Math.abs(this.metrics.currentRTP - this.config.targetRTP);
    
    // Check if deviation exceeds tolerance
    if (rtpDeviation > this.config.toleranceRange) {
      this.adjustRTP();
    }
  }

  /**
   * Adjust RTP by modifying the adjustment factor
   */
  private adjustRTP(): void {
    const rtpDifference = this.config.targetRTP - this.metrics.currentRTP;
    
    // Calculate adjustment factor
    // If RTP is too low, increase payouts; if too high, decrease payouts
    const adjustmentStrength = 0.1; // How aggressively to adjust (10%)
    const adjustment = (rtpDifference / this.config.targetRTP) * adjustmentStrength;
    
    this.adjustmentFactor = Math.max(0.5, Math.min(2.0, this.adjustmentFactor + adjustment));
    this.metrics.adjustmentFactor = this.adjustmentFactor;
    this.metrics.lastAdjustment = Date.now();

    if (this.debugMode) {
      console.log('📊 RTP Adjustment:', {
        currentRTP: this.metrics.currentRTP.toFixed(2) + '%',
        targetRTP: this.config.targetRTP + '%',
        difference: rtpDifference.toFixed(2) + '%',
        newAdjustmentFactor: this.adjustmentFactor.toFixed(3)
      });
    }
  }

  /**
   * Get payout adjustment factor for current RTP state
   */
  public getPayoutAdjustment(): number {
    return this.adjustmentFactor;
  }

  /**
   * Determine if a spin should result in a win based on volatility profile
   */
  public shouldWin(): boolean {
    const profile = this.volatilityProfiles.get(this.config.volatility);
    if (!profile) return false;

    // Adjust hit frequency based on current RTP
    let adjustedHitFrequency = profile.hitFrequency;
    
    if (this.metrics.currentRTP < this.config.targetRTP - this.config.toleranceRange) {
      // RTP is too low, increase win frequency slightly
      adjustedHitFrequency *= 1.1;
    } else if (this.metrics.currentRTP > this.config.targetRTP + this.config.toleranceRange) {
      // RTP is too high, decrease win frequency slightly
      adjustedHitFrequency *= 0.9;
    }

    return gameRNG.randomFloat(0, 100) < adjustedHitFrequency;
  }

  /**
   * Generate win multiplier based on volatility profile
   */
  public generateWinMultiplier(): number {
    const profile = this.volatilityProfiles.get(this.config.volatility);
    if (!profile) return 1;

    const random = gameRNG.randomFloat(0, 100);
    let cumulative = 0;

    // Determine win range based on distribution
    for (const [range, percentage] of Object.entries(profile.winDistribution)) {
      cumulative += percentage;
      if (random <= cumulative) {
        return this.generateMultiplierInRange(range);
      }
    }

    // Fallback to small win
    return gameRNG.randomFloat(1, 2);
  }

  /**
   * Generate multiplier within a specific range
   */
  private generateMultiplierInRange(range: string): number {
    switch (range) {
      case '1x-2x':
        return gameRNG.randomFloat(1, 2);
      case '2x-5x':
        return gameRNG.randomFloat(2, 5);
      case '5x-10x':
        return gameRNG.randomFloat(5, 10);
      case '10x+':
        const profile = this.volatilityProfiles.get(this.config.volatility)!;
        return gameRNG.randomFloat(10, profile.maxWinMultiplier);
      default:
        return 1;
    }
  }

  /**
   * Check if bonus feature should trigger
   */
  public shouldTriggerBonus(): boolean {
    const profile = this.volatilityProfiles.get(this.config.volatility);
    if (!profile) return false;

    return gameRNG.randomFloat(0, 100) < profile.bonusFrequency;
  }

  /**
   * Get current RTP metrics
   */
  public getMetrics(): RTPMetrics {
    return { ...this.metrics };
  }

  /**
   * Get volatility profile
   */
  public getVolatilityProfile(): VolatilityProfile | undefined {
    return this.volatilityProfiles.get(this.config.volatility);
  }

  /**
   * Reset RTP tracking (for testing or new sessions)
   */
  public reset(): void {
    this.metrics = {
      currentRTP: 0,
      totalSpins: 0,
      totalWagered: 0,
      totalPaidOut: 0,
      hitFrequency: 0,
      averageWin: 0,
      maxWin: 0,
      lastAdjustment: Date.now(),
      adjustmentFactor: 1.0,
      volatilityScore: 0
    };
    
    this.spinHistory = [];
    this.adjustmentFactor = 1.0;

    console.log('📊 RTPController: Reset completed');
  }

  /**
   * Update RTP configuration
   */
  public updateConfig(newConfig: Partial<RTPConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('📊 RTPController: Configuration updated');
  }

  /**
   * Export RTP data for analysis
   */
  public exportData(): {
    config: RTPConfig;
    metrics: RTPMetrics;
    recentSpins: SpinOutcome[];
    volatilityProfile: VolatilityProfile | undefined;
  } {
    return {
      config: this.config,
      metrics: this.metrics,
      recentSpins: this.spinHistory.slice(-100), // Last 100 spins
      volatilityProfile: this.getVolatilityProfile()
    };
  }

  /**
   * Simulate RTP over many spins (for testing)
   */
  public simulateRTP(spins: number, betAmount: number = 1): {
    finalRTP: number;
    hitFrequency: number;
    totalPayout: number;
    maxWin: number;
    volatilityScore: number;
  } {
    const originalHistory = [...this.spinHistory];
    const originalMetrics = { ...this.metrics };

    // Reset for simulation
    this.reset();

    for (let i = 0; i < spins; i++) {
      const shouldWin = this.shouldWin();
      let payout = 0;
      let winMultiplier = 0;

      if (shouldWin) {
        winMultiplier = this.generateWinMultiplier();
        payout = betAmount * winMultiplier * this.getPayoutAdjustment();
      }

      this.recordSpin({
        wagered: betAmount,
        payout,
        isWin: shouldWin,
        winMultiplier,
        bonusTriggered: this.shouldTriggerBonus(),
        timestamp: Date.now()
      });
    }

    const result = {
      finalRTP: this.metrics.currentRTP,
      hitFrequency: this.metrics.hitFrequency,
      totalPayout: this.metrics.totalPaidOut,
      maxWin: this.metrics.maxWin,
      volatilityScore: this.metrics.volatilityScore
    };

    // Restore original state
    this.spinHistory = originalHistory;
    this.metrics = originalMetrics;

    return result;
  }
}
