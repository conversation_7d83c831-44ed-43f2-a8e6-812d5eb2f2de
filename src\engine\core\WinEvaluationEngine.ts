/**
 * Advanced Win Evaluation Engine for Slot Machine Games
 * Implements sophisticated win detection logic with:
 * - Multiple pay mechanisms (paylines, ways-to-win, cluster pays)
 * - Special feature triggers
 * - Win precedence and combination rules
 * - Comprehensive win analysis
 */

import { PaytableSystem, WinCombination, PaylineDefinition } from './PaytableSystem';
import { gameRNG } from './RNG';

export interface SpinResult {
  grid: string[][];
  wins: WinCombination[];
  totalPayout: number;
  totalMultiplier: number;
  bonusTriggered: boolean;
  freeSpinsTriggered: boolean;
  specialFeatures: SpecialFeature[];
  hitFrequency: number;
  winType: 'no-win' | 'small-win' | 'medium-win' | 'big-win' | 'mega-win' | 'jackpot';
}

export interface SpecialFeature {
  type: 'expanding-wild' | 'sticky-wild' | 'multiplier' | 'respin' | 'bonus-game' | 'free-spins';
  symbolId?: string;
  positions?: Array<{ reel: number; row: number }>;
  multiplier?: number;
  count?: number;
  data?: any;
}

export interface EvaluationConfig {
  payMechanism: 'paylines' | 'ways-to-win' | 'cluster-pays' | 'scatter-pays';
  minClusterSize?: number;
  adjacencyType?: 'orthogonal' | 'diagonal' | 'both';
  cascadingWins?: boolean;
  winCap?: number; // Maximum win as multiplier of bet
  enableSpecialFeatures?: boolean;
}

export class WinEvaluationEngine {
  private paytableSystem: PaytableSystem;
  private config: EvaluationConfig;
  private debugMode: boolean;

  constructor(
    paytableSystem: PaytableSystem, 
    config: EvaluationConfig,
    debugMode: boolean = false
  ) {
    this.paytableSystem = paytableSystem;
    this.config = config;
    this.debugMode = debugMode;

    console.log('🎯 WinEvaluationEngine: Initialized with mechanism:', config.payMechanism);
  }

  /**
   * Evaluate a spin result for all types of wins
   */
  public evaluateSpin(
    grid: string[][],
    betPerLine: number,
    totalBet: number,
    activePaylines?: number[]
  ): SpinResult {
    if (this.debugMode) {
      console.log('🎯 Evaluating spin:', { grid, betPerLine, totalBet });
    }

    const result: SpinResult = {
      grid,
      wins: [],
      totalPayout: 0,
      totalMultiplier: 1,
      bonusTriggered: false,
      freeSpinsTriggered: false,
      specialFeatures: [],
      hitFrequency: 0,
      winType: 'no-win'
    };

    // Evaluate based on pay mechanism
    switch (this.config.payMechanism) {
      case 'paylines':
        this.evaluatePaylineWins(result, betPerLine, activePaylines);
        break;
      case 'ways-to-win':
        this.evaluateWaysToWin(result, betPerLine);
        break;
      case 'cluster-pays':
        this.evaluateClusterPays(result, totalBet);
        break;
      case 'scatter-pays':
        this.evaluateScatterPays(result, totalBet);
        break;
    }

    // Always check for scatter wins (independent of pay mechanism)
    if (this.config.payMechanism !== 'scatter-pays') {
      const scatterWins = this.paytableSystem.evaluateScatterWins(grid, totalBet);
      result.wins.push(...scatterWins);
    }

    // Evaluate special features
    if (this.config.enableSpecialFeatures) {
      this.evaluateSpecialFeatures(result);
    }

    // Calculate totals and determine win type
    this.calculateTotals(result, totalBet);

    if (this.debugMode && result.wins.length > 0) {
      console.log('🎯 Wins found:', result.wins);
      console.log('🎯 Total payout:', result.totalPayout);
    }

    return result;
  }

  /**
   * Evaluate payline-based wins
   */
  private evaluatePaylineWins(
    result: SpinResult,
    betPerLine: number,
    activePaylines?: number[]
  ): void {
    const paylines = this.paytableSystem.getActivePaylines();
    
    paylines.forEach(payline => {
      // Skip if not in active paylines list
      if (activePaylines && !activePaylines.includes(payline.id)) {
        return;
      }

      const win = this.paytableSystem.evaluatePayline(
        result.grid,
        payline.id,
        betPerLine
      );

      if (win) {
        result.wins.push(win);
      }
    });
  }

  /**
   * Evaluate ways-to-win (243 ways, 1024 ways, etc.)
   */
  private evaluateWaysToWin(result: SpinResult, betPerLine: number): void {
    const grid = result.grid;
    const reels = grid[0]?.length || 0;
    const rows = grid.length;

    // For each symbol type, find all possible ways
    const symbolCounts = new Map<string, number>();
    
    // Count symbols in each reel
    for (let reel = 0; reel < reels; reel++) {
      const reelSymbols = new Set<string>();
      for (let row = 0; row < rows; row++) {
        if (grid[row] && grid[row][reel]) {
          reelSymbols.add(grid[row][reel]);
        }
      }
      
      // For each unique symbol in this reel, check if it continues the sequence
      reelSymbols.forEach(symbol => {
        const currentCount = symbolCounts.get(symbol) || 0;
        if (currentCount === reel) { // Symbol must be consecutive from reel 0
          symbolCounts.set(symbol, currentCount + 1);
        }
      });
    }

    // Convert symbol counts to wins
    symbolCounts.forEach((count, symbolId) => {
      if (count >= 3) { // Minimum 3 symbols for a win
        const payout = this.paytableSystem.calculateSymbolPayout(
          symbolId,
          count,
          betPerLine
        );

        if (payout > 0) {
          // Find actual positions for this win
          const positions = this.findWaysPositions(grid, symbolId, count);
          
          result.wins.push({
            paylineId: -1, // Ways don't use paylines
            symbolId,
            count,
            positions,
            payout,
            multiplier: 1,
            isWildWin: this.isWildInvolved(positions, grid),
            isScatterWin: false
          });
        }
      }
    });
  }

  /**
   * Find positions for ways-to-win
   */
  private findWaysPositions(
    grid: string[][],
    symbolId: string,
    count: number
  ): Array<{ reel: number; row: number }> {
    const positions: Array<{ reel: number; row: number }> = [];
    
    for (let reel = 0; reel < count; reel++) {
      for (let row = 0; row < grid.length; row++) {
        if (grid[row] && grid[row][reel] === symbolId) {
          positions.push({ reel, row });
          break; // Only need one position per reel for ways
        }
      }
    }
    
    return positions;
  }

  /**
   * Evaluate cluster pays (adjacent matching symbols)
   */
  private evaluateClusterPays(result: SpinResult, totalBet: number): void {
    const grid = result.grid;
    const visited = grid.map(row => row.map(() => false));
    const clusters = new Map<string, Array<{ reel: number; row: number }>>();

    // Find all clusters using flood fill
    for (let row = 0; row < grid.length; row++) {
      for (let reel = 0; reel < grid[row].length; reel++) {
        if (!visited[row][reel]) {
          const symbol = grid[row][reel];
          const cluster = this.findCluster(grid, visited, reel, row, symbol);
          
          if (cluster.length >= (this.config.minClusterSize || 5)) {
            const existingClusters = clusters.get(symbol) || [];
            existingClusters.push(...cluster);
            clusters.set(symbol, existingClusters);
          }
        }
      }
    }

    // Convert clusters to wins
    clusters.forEach((positions, symbolId) => {
      const clusterSize = positions.length;
      const symbol = this.paytableSystem.getSymbol(symbolId);
      
      if (symbol && symbol.payouts[clusterSize]) {
        const payout = symbol.payouts[clusterSize] * totalBet;
        
        result.wins.push({
          paylineId: -1,
          symbolId,
          count: clusterSize,
          positions,
          payout,
          multiplier: 1,
          isWildWin: this.isWildInvolved(positions, grid),
          isScatterWin: false
        });
      }
    });
  }

  /**
   * Find cluster using flood fill algorithm
   */
  private findCluster(
    grid: string[][],
    visited: boolean[][],
    startReel: number,
    startRow: number,
    targetSymbol: string
  ): Array<{ reel: number; row: number }> {
    const cluster: Array<{ reel: number; row: number }> = [];
    const stack = [{ reel: startReel, row: startRow }];

    while (stack.length > 0) {
      const { reel, row } = stack.pop()!;
      
      if (
        row < 0 || row >= grid.length ||
        reel < 0 || reel >= grid[row].length ||
        visited[row][reel] ||
        grid[row][reel] !== targetSymbol
      ) {
        continue;
      }

      visited[row][reel] = true;
      cluster.push({ reel, row });

      // Add adjacent positions
      const adjacentPositions = this.getAdjacentPositions(reel, row);
      stack.push(...adjacentPositions);
    }

    return cluster;
  }

  /**
   * Get adjacent positions based on adjacency type
   */
  private getAdjacentPositions(reel: number, row: number): Array<{ reel: number; row: number }> {
    const positions: Array<{ reel: number; row: number }> = [];
    
    // Orthogonal adjacency (up, down, left, right)
    if (this.config.adjacencyType === 'orthogonal' || this.config.adjacencyType === 'both') {
      positions.push(
        { reel: reel - 1, row },
        { reel: reel + 1, row },
        { reel, row: row - 1 },
        { reel, row: row + 1 }
      );
    }

    // Diagonal adjacency
    if (this.config.adjacencyType === 'diagonal' || this.config.adjacencyType === 'both') {
      positions.push(
        { reel: reel - 1, row: row - 1 },
        { reel: reel - 1, row: row + 1 },
        { reel: reel + 1, row: row - 1 },
        { reel: reel + 1, row: row + 1 }
      );
    }

    return positions;
  }

  /**
   * Evaluate scatter pays only
   */
  private evaluateScatterPays(result: SpinResult, totalBet: number): void {
    const scatterWins = this.paytableSystem.evaluateScatterWins(result.grid, totalBet);
    result.wins.push(...scatterWins);
  }

  /**
   * Evaluate special features
   */
  private evaluateSpecialFeatures(result: SpinResult): void {
    // Check for expanding wilds
    this.checkExpandingWilds(result);
    
    // Check for bonus triggers
    this.checkBonusTriggers(result);
    
    // Check for free spin triggers
    this.checkFreeSpinTriggers(result);
    
    // Check for multipliers
    this.checkMultipliers(result);
  }

  /**
   * Check for expanding wild features
   */
  private checkExpandingWilds(result: SpinResult): void {
    const grid = result.grid;
    const wildSymbols = this.paytableSystem.getSymbolsByType('wild');
    
    wildSymbols.forEach(wildSymbol => {
      if (wildSymbol.specialFeatures?.expandingWild) {
        // Find wild positions
        for (let row = 0; row < grid.length; row++) {
          for (let reel = 0; reel < grid[row].length; reel++) {
            if (grid[row][reel] === wildSymbol.id) {
              // Expand wild to entire reel
              const expandedPositions: Array<{ reel: number; row: number }> = [];
              for (let r = 0; r < grid.length; r++) {
                grid[r][reel] = wildSymbol.id;
                expandedPositions.push({ reel, row: r });
              }
              
              result.specialFeatures.push({
                type: 'expanding-wild',
                symbolId: wildSymbol.id,
                positions: expandedPositions
              });
            }
          }
        }
      }
    });
  }

  /**
   * Check for bonus game triggers
   */
  private checkBonusTriggers(result: SpinResult): void {
    const bonusSymbols = this.paytableSystem.getSymbolsByType('bonus');
    
    bonusSymbols.forEach(bonusSymbol => {
      const positions: Array<{ reel: number; row: number }> = [];
      
      // Count bonus symbols
      result.grid.forEach((row, rowIndex) => {
        row.forEach((symbol, reelIndex) => {
          if (symbol === bonusSymbol.id) {
            positions.push({ reel: reelIndex, row: rowIndex });
          }
        });
      });

      // Check if enough bonus symbols to trigger
      if (positions.length >= 3) {
        result.bonusTriggered = true;
        result.specialFeatures.push({
          type: 'bonus-game',
          symbolId: bonusSymbol.id,
          positions,
          count: positions.length
        });
      }
    });
  }

  /**
   * Check for free spin triggers
   */
  private checkFreeSpinTriggers(result: SpinResult): void {
    const scatterSymbols = this.paytableSystem.getSymbolsByType('scatter');
    
    scatterSymbols.forEach(scatterSymbol => {
      const positions: Array<{ reel: number; row: number }> = [];
      
      // Count scatter symbols
      result.grid.forEach((row, rowIndex) => {
        row.forEach((symbol, reelIndex) => {
          if (symbol === scatterSymbol.id) {
            positions.push({ reel: reelIndex, row: rowIndex });
          }
        });
      });

      // Check if enough scatters to trigger free spins
      if (positions.length >= 3) {
        result.freeSpinsTriggered = true;
        
        // Calculate free spins count based on scatter count
        const freeSpinsCount = this.calculateFreeSpins(positions.length);
        
        result.specialFeatures.push({
          type: 'free-spins',
          symbolId: scatterSymbol.id,
          positions,
          count: freeSpinsCount
        });
      }
    });
  }

  /**
   * Calculate free spins based on scatter count
   */
  private calculateFreeSpins(scatterCount: number): number {
    const freeSpinsTable: { [count: number]: number } = {
      3: 10,
      4: 15,
      5: 20
    };
    
    return freeSpinsTable[scatterCount] || 10;
  }

  /**
   * Check for multiplier features
   */
  private checkMultipliers(result: SpinResult): void {
    // Check if any wins involve symbols with multiplier features
    result.wins.forEach(win => {
      const symbol = this.paytableSystem.getSymbol(win.symbolId);
      if (symbol?.specialFeatures?.multiplier) {
        win.multiplier *= symbol.specialFeatures.multiplier;
        result.totalMultiplier *= symbol.specialFeatures.multiplier;
        
        result.specialFeatures.push({
          type: 'multiplier',
          symbolId: win.symbolId,
          multiplier: symbol.specialFeatures.multiplier,
          positions: win.positions
        });
      }
    });
  }

  /**
   * Check if wild symbols are involved in positions
   */
  private isWildInvolved(
    positions: Array<{ reel: number; row: number }>,
    grid: string[][]
  ): boolean {
    return positions.some(pos => {
      const symbol = grid[pos.row]?.[pos.reel];
      const symbolDef = this.paytableSystem.getSymbol(symbol);
      return symbolDef?.isWild || false;
    });
  }

  /**
   * Calculate totals and determine win type
   */
  private calculateTotals(result: SpinResult, totalBet: number): void {
    // Calculate total payout
    result.totalPayout = result.wins.reduce((total, win) => total + win.payout, 0);
    
    // Apply total multiplier
    result.totalPayout *= result.totalMultiplier;
    
    // Determine win type based on payout relative to bet
    const winMultiplier = result.totalPayout / totalBet;
    
    if (winMultiplier === 0) {
      result.winType = 'no-win';
    } else if (winMultiplier < 5) {
      result.winType = 'small-win';
    } else if (winMultiplier < 25) {
      result.winType = 'medium-win';
    } else if (winMultiplier < 100) {
      result.winType = 'big-win';
    } else if (winMultiplier < 1000) {
      result.winType = 'mega-win';
    } else {
      result.winType = 'jackpot';
    }
    
    // Calculate hit frequency (simplified)
    result.hitFrequency = result.wins.length > 0 ? 1 : 0;
    
    // Apply win cap if configured
    if (this.config.winCap && winMultiplier > this.config.winCap) {
      result.totalPayout = totalBet * this.config.winCap;
    }
  }

  /**
   * Update evaluation configuration
   */
  public updateConfig(newConfig: Partial<EvaluationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🎯 WinEvaluationEngine: Configuration updated');
  }

  /**
   * Get current configuration
   */
  public getConfig(): EvaluationConfig {
    return { ...this.config };
  }
}
