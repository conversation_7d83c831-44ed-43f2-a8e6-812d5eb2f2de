/**
 * Audit and Compliance System for Slot Machine Games
 * Provides comprehensive logging, reporting, and verification capabilities
 * to meet gaming industry regulatory requirements and ensure fair play.
 */

import { gameRNG, RNGStatistics } from './RNG';
import { RTPMetrics } from './RTPController';

export interface AuditEntry {
  id: string;
  timestamp: number;
  type: 'spin' | 'bonus' | 'rtp-adjustment' | 'config-change' | 'error' | 'system';
  sessionId: string;
  playerId?: string;
  data: any;
  hash: string;
}

export interface SpinAuditData {
  spinId: string;
  bet: number;
  lines: number;
  grid: string[][];
  wins: Array<{
    paylineId: number;
    symbolId: string;
    count: number;
    payout: number;
  }>;
  totalPayout: number;
  bonusTriggered: boolean;
  freeSpinsTriggered: boolean;
  rngSequence: number;
  rtpBefore: number;
  rtpAfter: number;
  balanceBefore: number;
  balanceAfter: number;
}

export interface ComplianceReport {
  reportId: string;
  generatedAt: number;
  period: { start: number; end: number };
  totalSpins: number;
  totalWagered: number;
  totalPaidOut: number;
  actualRTP: number;
  targetRTP: number;
  rtpDeviation: number;
  hitFrequency: number;
  maxWin: number;
  bonusFrequency: number;
  rngStatistics: RNGStatistics;
  volatilityMetrics: {
    variance: number;
    standardDeviation: number;
    winDistribution: { [range: string]: number };
  };
  complianceStatus: 'PASS' | 'FAIL' | 'WARNING';
  issues: string[];
  recommendations: string[];
}

export interface AuditConfig {
  enableLogging: boolean;
  maxLogEntries: number;
  enableRealTimeMonitoring: boolean;
  complianceThresholds: {
    rtpDeviationWarning: number; // ±% from target RTP
    rtpDeviationError: number;
    hitFrequencyDeviationWarning: number;
    hitFrequencyDeviationError: number;
    maxWinThreshold: number; // Maximum win as multiplier of bet
  };
  reportingInterval: number; // Minutes between automatic reports
  retentionPeriod: number; // Days to retain audit logs
}

export class AuditSystem {
  private auditLog: AuditEntry[] = [];
  private config: AuditConfig;
  private sessionId: string;
  private debugMode: boolean;
  private lastReportTime: number = 0;
  private spinCount: number = 0;
  private totalWagered: number = 0;
  private totalPaidOut: number = 0;
  private maxWin: number = 0;
  private bonusCount: number = 0;

  constructor(config: AuditConfig, debugMode: boolean = false) {
    this.config = config;
    this.debugMode = debugMode;
    this.sessionId = this.generateSessionId();
    
    // Log system initialization
    this.logEntry('system', {
      event: 'audit_system_initialized',
      config: this.config,
      sessionId: this.sessionId
    });

    console.log('📋 AuditSystem: Initialized with session ID:', this.sessionId);
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `session_${timestamp}_${random}`;
  }

  /**
   * Generate cryptographic hash for audit entry
   */
  private generateHash(data: any): string {
    const jsonString = JSON.stringify(data);
    let hash = 0;
    
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(16);
  }

  /**
   * Log an audit entry
   */
  public logEntry(
    type: AuditEntry['type'],
    data: any,
    playerId?: string
  ): string {
    const id = `audit_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    const timestamp = Date.now();
    
    const entry: AuditEntry = {
      id,
      timestamp,
      type,
      sessionId: this.sessionId,
      playerId,
      data,
      hash: this.generateHash({ id, timestamp, type, sessionId: this.sessionId, data })
    };

    this.auditLog.push(entry);

    // Trim log if it exceeds max entries
    if (this.auditLog.length > this.config.maxLogEntries) {
      this.auditLog.shift();
    }

    if (this.debugMode && type !== 'spin') {
      console.log('📋 Audit entry logged:', { type, id, dataKeys: Object.keys(data) });
    }

    return id;
  }

  /**
   * Log a spin for audit purposes
   */
  public logSpin(spinData: SpinAuditData, playerId?: string): string {
    this.spinCount++;
    this.totalWagered += spinData.bet * spinData.lines;
    this.totalPaidOut += spinData.totalPayout;
    
    if (spinData.totalPayout > this.maxWin) {
      this.maxWin = spinData.totalPayout;
    }
    
    if (spinData.bonusTriggered || spinData.freeSpinsTriggered) {
      this.bonusCount++;
    }

    return this.logEntry('spin', spinData, playerId);
  }

  /**
   * Log RTP adjustment
   */
  public logRTPAdjustment(
    oldFactor: number,
    newFactor: number,
    reason: string,
    rtpMetrics: RTPMetrics
  ): string {
    return this.logEntry('rtp-adjustment', {
      oldAdjustmentFactor: oldFactor,
      newAdjustmentFactor: newFactor,
      reason,
      rtpMetrics,
      timestamp: Date.now()
    });
  }

  /**
   * Log configuration change
   */
  public logConfigChange(
    component: string,
    oldConfig: any,
    newConfig: any,
    reason?: string
  ): string {
    return this.logEntry('config-change', {
      component,
      oldConfig,
      newConfig,
      reason,
      timestamp: Date.now()
    });
  }

  /**
   * Log bonus feature activation
   */
  public logBonusActivation(
    bonusType: string,
    triggerData: any,
    award: number
  ): string {
    return this.logEntry('bonus', {
      event: 'bonus_activated',
      bonusType,
      triggerData,
      award,
      timestamp: Date.now()
    });
  }

  /**
   * Log error or exception
   */
  public logError(
    error: Error | string,
    context?: any
  ): string {
    return this.logEntry('error', {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : error,
      context,
      timestamp: Date.now()
    });
  }

  /**
   * Generate compliance report
   */
  public generateComplianceReport(
    targetRTP: number,
    period?: { start: number; end: number }
  ): ComplianceReport {
    const reportId = `report_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    const now = Date.now();
    
    // Filter entries by period if specified
    let relevantEntries = this.auditLog;
    if (period) {
      relevantEntries = this.auditLog.filter(entry => 
        entry.timestamp >= period.start && entry.timestamp <= period.end
      );
    }

    const spinEntries = relevantEntries.filter(entry => entry.type === 'spin');
    
    // Calculate metrics
    const totalSpins = spinEntries.length;
    const totalWagered = spinEntries.reduce((sum, entry) => 
      sum + (entry.data.bet * entry.data.lines), 0);
    const totalPaidOut = spinEntries.reduce((sum, entry) => 
      sum + entry.data.totalPayout, 0);
    
    const actualRTP = totalWagered > 0 ? (totalPaidOut / totalWagered) * 100 : 0;
    const rtpDeviation = Math.abs(actualRTP - targetRTP);
    
    const winsCount = spinEntries.filter(entry => entry.data.totalPayout > 0).length;
    const hitFrequency = totalSpins > 0 ? (winsCount / totalSpins) * 100 : 0;
    
    const maxWin = Math.max(...spinEntries.map(entry => entry.data.totalPayout), 0);
    
    const bonusEntries = relevantEntries.filter(entry => 
      entry.type === 'bonus' && entry.data.event === 'bonus_activated'
    );
    const bonusFrequency = totalSpins > 0 ? (bonusEntries.length / totalSpins) * 100 : 0;

    // Calculate volatility metrics
    const winAmounts = spinEntries
      .filter(entry => entry.data.totalPayout > 0)
      .map(entry => entry.data.totalPayout);
    
    const mean = winAmounts.length > 0 ? 
      winAmounts.reduce((sum, win) => sum + win, 0) / winAmounts.length : 0;
    
    const variance = winAmounts.length > 0 ? 
      winAmounts.reduce((sum, win) => sum + Math.pow(win - mean, 2), 0) / winAmounts.length : 0;
    
    const standardDeviation = Math.sqrt(variance);

    // Win distribution analysis
    const winDistribution: { [range: string]: number } = {
      '0x': 0, '1x-2x': 0, '2x-5x': 0, '5x-10x': 0, '10x-25x': 0, '25x+': 0
    };

    spinEntries.forEach(entry => {
      const winMultiplier = entry.data.totalPayout / (entry.data.bet * entry.data.lines);
      if (winMultiplier === 0) winDistribution['0x']++;
      else if (winMultiplier < 2) winDistribution['1x-2x']++;
      else if (winMultiplier < 5) winDistribution['2x-5x']++;
      else if (winMultiplier < 10) winDistribution['5x-10x']++;
      else if (winMultiplier < 25) winDistribution['10x-25x']++;
      else winDistribution['25x+']++;
    });

    // Determine compliance status
    let complianceStatus: 'PASS' | 'FAIL' | 'WARNING' = 'PASS';
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check RTP compliance
    if (rtpDeviation > this.config.complianceThresholds.rtpDeviationError) {
      complianceStatus = 'FAIL';
      issues.push(`RTP deviation (${rtpDeviation.toFixed(2)}%) exceeds error threshold`);
    } else if (rtpDeviation > this.config.complianceThresholds.rtpDeviationWarning) {
      if (complianceStatus === 'PASS') complianceStatus = 'WARNING';
      issues.push(`RTP deviation (${rtpDeviation.toFixed(2)}%) exceeds warning threshold`);
      recommendations.push('Monitor RTP closely and consider adjustment if trend continues');
    }

    // Check maximum win compliance
    const maxWinMultiplier = totalWagered > 0 ? maxWin / (totalWagered / totalSpins) : 0;
    if (maxWinMultiplier > this.config.complianceThresholds.maxWinThreshold) {
      if (complianceStatus === 'PASS') complianceStatus = 'WARNING';
      issues.push(`Maximum win (${maxWinMultiplier.toFixed(2)}x) exceeds threshold`);
      recommendations.push('Review win cap settings and payout structure');
    }

    // Get RNG statistics
    const rngStatistics = gameRNG.getStatistics();

    const report: ComplianceReport = {
      reportId,
      generatedAt: now,
      period: period || { start: 0, end: now },
      totalSpins,
      totalWagered,
      totalPaidOut,
      actualRTP,
      targetRTP,
      rtpDeviation,
      hitFrequency,
      maxWin,
      bonusFrequency,
      rngStatistics,
      volatilityMetrics: {
        variance,
        standardDeviation,
        winDistribution
      },
      complianceStatus,
      issues,
      recommendations
    };

    // Log report generation
    this.logEntry('system', {
      event: 'compliance_report_generated',
      reportId,
      complianceStatus,
      issuesCount: issues.length
    });

    return report;
  }

  /**
   * Verify audit log integrity
   */
  public verifyLogIntegrity(): {
    valid: boolean;
    corruptedEntries: string[];
    totalEntries: number;
  } {
    const corruptedEntries: string[] = [];
    
    this.auditLog.forEach(entry => {
      const expectedHash = this.generateHash({
        id: entry.id,
        timestamp: entry.timestamp,
        type: entry.type,
        sessionId: entry.sessionId,
        data: entry.data
      });
      
      if (entry.hash !== expectedHash) {
        corruptedEntries.push(entry.id);
      }
    });

    const valid = corruptedEntries.length === 0;
    
    if (!valid) {
      this.logError('Audit log integrity check failed', {
        corruptedEntries,
        totalEntries: this.auditLog.length
      });
    }

    return {
      valid,
      corruptedEntries,
      totalEntries: this.auditLog.length
    };
  }

  /**
   * Export audit data for external analysis
   */
  public exportAuditData(
    startTime?: number,
    endTime?: number
  ): {
    sessionId: string;
    exportedAt: number;
    entries: AuditEntry[];
    summary: {
      totalEntries: number;
      entryTypes: { [type: string]: number };
      timeRange: { start: number; end: number };
    };
  } {
    let entries = this.auditLog;
    
    if (startTime || endTime) {
      entries = this.auditLog.filter(entry => {
        if (startTime && entry.timestamp < startTime) return false;
        if (endTime && entry.timestamp > endTime) return false;
        return true;
      });
    }

    const entryTypes: { [type: string]: number } = {};
    entries.forEach(entry => {
      entryTypes[entry.type] = (entryTypes[entry.type] || 0) + 1;
    });

    const timeRange = {
      start: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : 0,
      end: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : 0
    };

    return {
      sessionId: this.sessionId,
      exportedAt: Date.now(),
      entries,
      summary: {
        totalEntries: entries.length,
        entryTypes,
        timeRange
      }
    };
  }

  /**
   * Get current session statistics
   */
  public getSessionStatistics(): {
    sessionId: string;
    startTime: number;
    duration: number;
    totalSpins: number;
    totalWagered: number;
    totalPaidOut: number;
    currentRTP: number;
    maxWin: number;
    bonusCount: number;
    bonusFrequency: number;
  } {
    const startTime = this.auditLog.length > 0 ? this.auditLog[0].timestamp : Date.now();
    const duration = Date.now() - startTime;
    const currentRTP = this.totalWagered > 0 ? (this.totalPaidOut / this.totalWagered) * 100 : 0;
    const bonusFrequency = this.spinCount > 0 ? (this.bonusCount / this.spinCount) * 100 : 0;

    return {
      sessionId: this.sessionId,
      startTime,
      duration,
      totalSpins: this.spinCount,
      totalWagered: this.totalWagered,
      totalPaidOut: this.totalPaidOut,
      currentRTP,
      maxWin: this.maxWin,
      bonusCount: this.bonusCount,
      bonusFrequency
    };
  }

  /**
   * Clear audit log (for testing or maintenance)
   */
  public clearLog(): void {
    const entriesCleared = this.auditLog.length;
    this.auditLog = [];
    this.spinCount = 0;
    this.totalWagered = 0;
    this.totalPaidOut = 0;
    this.maxWin = 0;
    this.bonusCount = 0;
    
    this.logEntry('system', {
      event: 'audit_log_cleared',
      entriesCleared,
      timestamp: Date.now()
    });

    console.log('📋 AuditSystem: Log cleared, entries removed:', entriesCleared);
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<AuditConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };
    
    this.logConfigChange('audit_system', oldConfig, this.config, 'Configuration updated');
    
    console.log('📋 AuditSystem: Configuration updated');
  }
}
