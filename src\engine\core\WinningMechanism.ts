/**
 * Industry-Standard Winning Mechanism for Slot Machine Games
 * Integrates all winning systems into a comprehensive, auditable mechanism
 * that meets gaming industry standards for fairness, RTP, and volatility.
 */

import { ProfessionalRNG, gameRNG } from './RNG';
import { PaytableSystem, SymbolDefinition, PaylineDefinition, WinCombination } from './PaytableSystem';
import { WinEvaluationEngine, SpinResult, EvaluationConfig } from './WinEvaluationEngine';
import { RTP<PERSON>ontroller, RTPConfig, SpinOutcome } from './RTPController';
import { BonusFeatureSystem } from './BonusFeatureSystem';
import { AuditSystem, AuditConfig, SpinAuditData } from './AuditSystem';

export interface GameConfiguration {
  // Grid Configuration
  reels: number;
  rows: number;
  
  // Symbol Configuration
  symbols: SymbolDefinition[];
  
  // Payline Configuration
  paylines: PaylineDefinition[];
  
  // RTP and Volatility
  rtp: RTPConfig;
  
  // Evaluation Settings
  evaluation: EvaluationConfig;
  
  // Betting Configuration
  betting: {
    minBet: number;
    maxBet: number;
    defaultBet: number;
    maxLines: number;
  };
  
  // Feature Configuration
  features: {
    autoplay: boolean;
    turboMode: boolean;
    maxWinCap: number;
    bonusGames: boolean;
    freeSpins: boolean;
  };
}

export interface GameState {
  balance: number;
  currentBet: number;
  activeLines: number;
  isSpinning: boolean;
  inBonus: boolean;
  freeSpinsRemaining: number;
  multiplier: number;
  lastWin: number;
  sessionStats: {
    totalSpins: number;
    totalWagered: number;
    totalWon: number;
    biggestWin: number;
    currentStreak: number;
  };
}

export interface SpinRequest {
  bet: number;
  lines: number;
  turbo?: boolean;
  autoplay?: boolean;
}

export interface GameSpinResult extends SpinResult {
  // Additional game-specific data
  balance: number;
  newBalance: number;
  sessionStats: GameState['sessionStats'];
  rtpMetrics: {
    currentRTP: number;
    targetRTP: number;
    hitFrequency: number;
    volatilityScore: number;
  };
  auditData: {
    spinId: string;
    timestamp: number;
    rngSequence: number;
    adjustmentFactor: number;
  };
}

export class WinningMechanism {
  private config: GameConfiguration;
  private paytableSystem: PaytableSystem;
  private winEvaluationEngine: WinEvaluationEngine;
  private rtpController: RTPController;
  private bonusFeatureSystem: BonusFeatureSystem;
  private auditSystem: AuditSystem;
  private gameState: GameState;
  private debugMode: boolean;
  private spinCounter: number = 0;

  constructor(config: GameConfiguration, debugMode: boolean = false) {
    this.config = config;
    this.debugMode = debugMode;
    
    // Initialize core systems
    this.initializeSystems();
    
    // Initialize game state
    this.initializeGameState();
    
    console.log('🎰 WinningMechanism: Initialized with configuration:', {
      reels: config.reels,
      rows: config.rows,
      symbols: config.symbols.length,
      paylines: config.paylines.length,
      targetRTP: config.rtp.targetRTP + '%',
      volatility: config.rtp.volatility
    });
  }

  /**
   * Initialize all core systems
   */
  private initializeSystems(): void {
    // Initialize paytable system
    this.paytableSystem = new PaytableSystem({
      symbols: this.config.symbols,
      paylines: this.config.paylines,
      baseRTP: this.config.rtp.targetRTP,
      volatility: this.config.rtp.volatility,
      hitFrequency: this.config.rtp.hitFrequency,
      maxWinMultiplier: this.config.rtp.maxWinMultiplier,
      scatterPayMultiplier: 2.0,
      wildMultiplier: 2.0
    });

    // Initialize win evaluation engine
    this.winEvaluationEngine = new WinEvaluationEngine(
      this.paytableSystem,
      this.config.evaluation,
      this.debugMode
    );

    // Initialize RTP controller
    this.rtpController = new RTPController(this.config.rtp, this.debugMode);

    // Initialize bonus feature system
    this.bonusFeatureSystem = new BonusFeatureSystem(this.paytableSystem, this.debugMode);

    // Initialize audit system
    const auditConfig: AuditConfig = {
      enableLogging: true,
      maxLogEntries: 10000,
      enableRealTimeMonitoring: true,
      complianceThresholds: {
        rtpDeviationWarning: 2.0,
        rtpDeviationError: 5.0,
        hitFrequencyDeviationWarning: 5.0,
        hitFrequencyDeviationError: 10.0,
        maxWinThreshold: 1000
      },
      reportingInterval: 60, // 1 hour
      retentionPeriod: 30 // 30 days
    };
    this.auditSystem = new AuditSystem(auditConfig, this.debugMode);
  }

  /**
   * Initialize game state
   */
  private initializeGameState(): void {
    this.gameState = {
      balance: 1000, // Default starting balance
      currentBet: this.config.betting.defaultBet,
      activeLines: this.config.paylines.length,
      isSpinning: false,
      inBonus: false,
      freeSpinsRemaining: 0,
      multiplier: 1,
      lastWin: 0,
      sessionStats: {
        totalSpins: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        currentStreak: 0
      }
    };
  }

  /**
   * Execute a spin with full winning mechanism
   */
  public async spin(request: SpinRequest): Promise<GameSpinResult> {
    if (this.gameState.isSpinning) {
      throw new Error('Spin already in progress');
    }

    // Validate spin request
    this.validateSpinRequest(request);

    // Set spinning state
    this.gameState.isSpinning = true;
    this.spinCounter++;

    try {
      // Calculate total bet
      const totalBet = request.bet * request.lines;
      const betPerLine = request.bet;

      // Check balance
      if (this.gameState.balance < totalBet) {
        throw new Error('Insufficient balance');
      }

      // Deduct bet from balance
      this.gameState.balance -= totalBet;
      this.gameState.currentBet = request.bet;
      this.gameState.activeLines = request.lines;

      // Update session stats
      this.gameState.sessionStats.totalSpins++;
      this.gameState.sessionStats.totalWagered += totalBet;

      // Store balance before spin for audit
      const balanceBefore = this.gameState.balance + totalBet;

      // Generate spin result
      const spinResult = await this.generateSpinResult(betPerLine, totalBet, request.lines);

      // Check for bonus features
      const bonusResult = this.bonusFeatureSystem.checkBonusTriggers(spinResult.grid, totalBet);
      if (bonusResult.triggered) {
        spinResult.bonusTriggered = true;
        spinResult.freeSpinsTriggered = bonusResult.featureType === 'free-spins';
        spinResult.totalPayout += bonusResult.award;

        // Log bonus activation
        this.auditSystem.logBonusActivation(
          bonusResult.featureType || 'unknown',
          { triggerSymbols: spinResult.grid },
          bonusResult.award
        );
      }

      // Process winnings
      this.processWinnings(spinResult, totalBet);

      // Record spin for RTP tracking
      this.recordSpinForRTP(totalBet, spinResult.totalPayout);

      // Update game state
      this.updateGameState(spinResult);

      // Create comprehensive result
      const gameResult = this.createGameSpinResult(spinResult, totalBet);

      // Log spin for audit
      const spinAuditData: SpinAuditData = {
        spinId: `spin_${this.spinCounter}`,
        bet: request.bet,
        lines: request.lines,
        grid: spinResult.grid,
        wins: spinResult.wins.map(win => ({
          paylineId: win.paylineId || 0,
          symbolId: win.symbolId,
          count: win.count,
          payout: win.payout
        })),
        totalPayout: spinResult.totalPayout,
        bonusTriggered: spinResult.bonusTriggered,
        freeSpinsTriggered: spinResult.freeSpinsTriggered,
        rngSequence: this.spinCounter,
        rtpBefore: this.rtpController.getMetrics().currentRTP,
        rtpAfter: this.rtpController.getMetrics().currentRTP,
        balanceBefore,
        balanceAfter: this.gameState.balance
      };
      this.auditSystem.logSpin(spinAuditData);

      // Log result if in debug mode
      if (this.debugMode) {
        this.logSpinResult(gameResult);
      }

      return gameResult;

    } finally {
      // Always clear spinning state
      this.gameState.isSpinning = false;
    }
  }

  /**
   * Validate spin request
   */
  private validateSpinRequest(request: SpinRequest): void {
    if (request.bet < this.config.betting.minBet || request.bet > this.config.betting.maxBet) {
      throw new Error(`Bet must be between ${this.config.betting.minBet} and ${this.config.betting.maxBet}`);
    }

    if (request.lines < 1 || request.lines > this.config.betting.maxLines) {
      throw new Error(`Lines must be between 1 and ${this.config.betting.maxLines}`);
    }
  }

  /**
   * Generate the actual spin result
   */
  private async generateSpinResult(
    betPerLine: number, 
    totalBet: number, 
    activeLines: number
  ): Promise<SpinResult> {
    // Generate grid using weighted symbol selection
    const grid = this.generateSpinGrid();

    // Get active paylines for this spin
    const activePaylineIds = this.config.paylines
      .slice(0, activeLines)
      .map(payline => payline.id);

    // Evaluate the spin for wins
    const spinResult = this.winEvaluationEngine.evaluateSpin(
      grid,
      betPerLine,
      totalBet,
      activePaylineIds
    );

    // Apply RTP adjustments
    this.applyRTPAdjustments(spinResult);

    return spinResult;
  }

  /**
   * Generate spin grid using paytable system
   */
  private generateSpinGrid(): string[][] {
    const grid: string[][] = [];
    
    for (let row = 0; row < this.config.rows; row++) {
      const gridRow: string[] = [];
      for (let reel = 0; reel < this.config.reels; reel++) {
        gridRow.push(this.paytableSystem.getWeightedSymbol());
      }
      grid.push(gridRow);
    }

    return grid;
  }

  /**
   * Apply RTP adjustments to spin result
   */
  private applyRTPAdjustments(spinResult: SpinResult): void {
    const adjustmentFactor = this.rtpController.getPayoutAdjustment();
    
    // Adjust all win payouts
    spinResult.wins.forEach(win => {
      win.payout *= adjustmentFactor;
    });
    
    // Recalculate total payout
    spinResult.totalPayout = spinResult.wins.reduce((total, win) => total + win.payout, 0);
    spinResult.totalPayout *= spinResult.totalMultiplier;
  }

  /**
   * Process winnings and update balance
   */
  private processWinnings(spinResult: SpinResult, totalBet: number): void {
    if (spinResult.totalPayout > 0) {
      // Add winnings to balance
      this.gameState.balance += spinResult.totalPayout;
      this.gameState.lastWin = spinResult.totalPayout;
      
      // Update session stats
      this.gameState.sessionStats.totalWon += spinResult.totalPayout;
      this.gameState.sessionStats.currentStreak++;
      
      if (spinResult.totalPayout > this.gameState.sessionStats.biggestWin) {
        this.gameState.sessionStats.biggestWin = spinResult.totalPayout;
      }
    } else {
      // No win - reset streak
      this.gameState.lastWin = 0;
      this.gameState.sessionStats.currentStreak = 0;
    }

    // Handle special features
    if (spinResult.freeSpinsTriggered) {
      const freeSpinsFeature = spinResult.specialFeatures.find(f => f.type === 'free-spins');
      if (freeSpinsFeature) {
        this.gameState.freeSpinsRemaining = freeSpinsFeature.count || 10;
      }
    }

    if (spinResult.bonusTriggered) {
      this.gameState.inBonus = true;
    }
  }

  /**
   * Record spin for RTP tracking
   */
  private recordSpinForRTP(wagered: number, payout: number): void {
    const outcome: SpinOutcome = {
      wagered,
      payout,
      isWin: payout > 0,
      winMultiplier: payout / wagered,
      bonusTriggered: this.gameState.inBonus,
      timestamp: Date.now()
    };

    this.rtpController.recordSpin(outcome);
  }

  /**
   * Update game state after spin
   */
  private updateGameState(spinResult: SpinResult): void {
    // Apply any multipliers from special features
    const multiplierFeatures = spinResult.specialFeatures.filter(f => f.type === 'multiplier');
    if (multiplierFeatures.length > 0) {
      this.gameState.multiplier = multiplierFeatures.reduce((total, feature) => 
        total * (feature.multiplier || 1), 1);
    } else {
      this.gameState.multiplier = 1;
    }

    // Handle free spins
    if (this.gameState.freeSpinsRemaining > 0) {
      this.gameState.freeSpinsRemaining--;
    }

    // Exit bonus if no more free spins
    if (this.gameState.freeSpinsRemaining === 0 && this.gameState.inBonus) {
      this.gameState.inBonus = false;
    }
  }

  /**
   * Create comprehensive game spin result
   */
  private createGameSpinResult(spinResult: SpinResult, totalBet: number): GameSpinResult {
    const rtpMetrics = this.rtpController.getMetrics();
    
    return {
      ...spinResult,
      balance: this.gameState.balance + totalBet, // Balance before spin
      newBalance: this.gameState.balance,
      sessionStats: { ...this.gameState.sessionStats },
      rtpMetrics: {
        currentRTP: rtpMetrics.currentRTP,
        targetRTP: this.config.rtp.targetRTP,
        hitFrequency: rtpMetrics.hitFrequency,
        volatilityScore: rtpMetrics.volatilityScore
      },
      auditData: {
        spinId: `spin_${this.spinCounter}_${Date.now()}`,
        timestamp: Date.now(),
        rngSequence: gameRNG.getStatistics().totalGenerations,
        adjustmentFactor: rtpMetrics.adjustmentFactor
      }
    };
  }

  /**
   * Log spin result for debugging
   */
  private logSpinResult(result: GameSpinResult): void {
    console.log('🎰 Spin Result:', {
      spinId: result.auditData.spinId,
      winType: result.winType,
      totalPayout: result.totalPayout.toFixed(2),
      balance: result.newBalance.toFixed(2),
      rtp: result.rtpMetrics.currentRTP.toFixed(2) + '%',
      wins: result.wins.length,
      bonusTriggered: result.bonusTriggered,
      freeSpinsTriggered: result.freeSpinsTriggered
    });
  }

  /**
   * Get current game state
   */
  public getGameState(): GameState {
    return { ...this.gameState };
  }

  /**
   * Set player balance
   */
  public setBalance(balance: number): void {
    this.gameState.balance = balance;
  }

  /**
   * Get RTP metrics
   */
  public getRTPMetrics() {
    return this.rtpController.getMetrics();
  }

  /**
   * Get paytable information
   */
  public getPaytable() {
    return this.paytableSystem.exportPaytable();
  }

  /**
   * Reset game session
   */
  public resetSession(): void {
    this.initializeGameState();
    this.rtpController.reset();
    this.spinCounter = 0;
    
    console.log('🎰 WinningMechanism: Session reset');
  }

  /**
   * Update game configuration
   */
  public updateConfiguration(newConfig: Partial<GameConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update subsystems
    if (newConfig.symbols || newConfig.paylines) {
      this.paytableSystem.updateConfig({
        symbols: this.config.symbols,
        paylines: this.config.paylines,
        baseRTP: this.config.rtp.targetRTP,
        volatility: this.config.rtp.volatility,
        hitFrequency: this.config.rtp.hitFrequency,
        maxWinMultiplier: this.config.rtp.maxWinMultiplier,
        scatterPayMultiplier: 2.0,
        wildMultiplier: 2.0
      });
    }

    if (newConfig.evaluation) {
      this.winEvaluationEngine.updateConfig(newConfig.evaluation);
    }

    if (newConfig.rtp) {
      this.rtpController.updateConfig(newConfig.rtp);
    }

    console.log('🎰 WinningMechanism: Configuration updated');
  }

  /**
   * Get bonus feature system
   */
  public getBonusFeatureSystem(): BonusFeatureSystem {
    return this.bonusFeatureSystem;
  }

  /**
   * Get audit system
   */
  public getAuditSystem(): AuditSystem {
    return this.auditSystem;
  }

  /**
   * Generate compliance report
   */
  public generateComplianceReport(): any {
    return this.auditSystem.generateComplianceReport(this.config.rtp.targetRTP);
  }

  /**
   * Export comprehensive audit data
   */
  public exportAuditData() {
    return {
      gameConfig: this.config,
      gameState: this.gameState,
      rtpData: this.rtpController.exportData(),
      rngStatistics: gameRNG.getStatistics(),
      paytable: this.paytableSystem.exportPaytable(),
      bonusFeatureData: this.bonusFeatureSystem.exportFeatureData(),
      auditData: this.auditSystem.getSessionStatistics(),
      spinCounter: this.spinCounter,
      timestamp: Date.now()
    };
  }

  /**
   * Test RTP over many simulated spins
   */
  public testRTP(spins: number = 10000): any {
    return this.rtpController.simulateRTP(spins, this.config.betting.defaultBet);
  }
}
