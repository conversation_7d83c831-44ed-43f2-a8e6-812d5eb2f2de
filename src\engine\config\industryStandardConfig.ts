/**
 * Industry-Standard Slot Machine Configuration
 * Professional-grade configuration that meets gaming industry standards
 * for RTP, volatility, symbol distribution, and game mathematics.
 */

import { GameConfiguration } from '../core/WinningMechanism';

/**
 * Standard 5x3 slot machine configuration with 25 paylines
 * RTP: 96.0% (industry standard)
 * Volatility: Medium
 * Hit Frequency: 25.5%
 */
export const STANDARD_5X3_CONFIG: GameConfiguration = {
  // Grid Configuration
  reels: 5,
  rows: 3,

  // Symbol Configuration (ordered by value: high to low)
  symbols: [
    // Wild Symbol
    {
      id: 'wild',
      name: 'Wild',
      type: 'wild',
      weight: 2, // Rare appearance
      payouts: { 3: 25, 4: 100, 5: 500 },
      isWild: true,
      canSubstitute: ['high1', 'high2', 'medium1', 'medium2', 'medium3', 'low1', 'low2', 'low3'],
      specialFeatures: {
        expandingWild: false,
        stickyWild: false,
        multiplier: 2
      }
    },

    // Scatter Symbol
    {
      id: 'scatter',
      name: 'Scatter',
      type: 'scatter',
      weight: 3, // Rare appearance
      payouts: { 3: 5, 4: 25, 5: 100 }, // Multiplied by total bet
      isScatter: true,
      specialFeatures: {
        triggerBonus: 'free-spins'
      }
    },

    // High Value Symbols
    {
      id: 'high1',
      name: 'Diamond',
      type: 'high',
      weight: 8,
      payouts: { 3: 15, 4: 50, 5: 200 }
    },
    {
      id: 'high2',
      name: 'Gold Bar',
      type: 'high',
      weight: 10,
      payouts: { 3: 12, 4: 40, 5: 150 }
    },

    // Medium Value Symbols
    {
      id: 'medium1',
      name: 'Ruby',
      type: 'medium',
      weight: 15,
      payouts: { 3: 8, 4: 25, 5: 75 }
    },
    {
      id: 'medium2',
      name: 'Emerald',
      type: 'medium',
      weight: 18,
      payouts: { 3: 6, 4: 20, 5: 60 }
    },
    {
      id: 'medium3',
      name: 'Sapphire',
      type: 'medium',
      weight: 20,
      payouts: { 3: 5, 4: 15, 5: 45 }
    },

    // Low Value Symbols
    {
      id: 'low1',
      name: 'Ace',
      type: 'low',
      weight: 25,
      payouts: { 3: 3, 4: 8, 5: 25 }
    },
    {
      id: 'low2',
      name: 'King',
      type: 'low',
      weight: 28,
      payouts: { 3: 2, 4: 6, 5: 20 }
    },
    {
      id: 'low3',
      name: 'Queen',
      type: 'low',
      weight: 30,
      payouts: { 3: 2, 4: 5, 5: 15 }
    }
  ],

  // Standard 25 Paylines for 5x3 Grid
  paylines: [
    // Straight lines
    { id: 1, name: 'Line 1', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 1 }, { reel: 2, row: 1 }, { reel: 3, row: 1 }, { reel: 4, row: 1 }], active: true },
    { id: 2, name: 'Line 2', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 0 }, { reel: 2, row: 0 }, { reel: 3, row: 0 }, { reel: 4, row: 0 }], active: true },
    { id: 3, name: 'Line 3', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 2 }, { reel: 2, row: 2 }, { reel: 3, row: 2 }, { reel: 4, row: 2 }], active: true },

    // V-shapes
    { id: 4, name: 'Line 4', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 1 }, { reel: 2, row: 2 }, { reel: 3, row: 1 }, { reel: 4, row: 0 }], active: true },
    { id: 5, name: 'Line 5', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 1 }, { reel: 2, row: 0 }, { reel: 3, row: 1 }, { reel: 4, row: 2 }], active: true },

    // Zigzag patterns
    { id: 6, name: 'Line 6', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 0 }, { reel: 2, row: 1 }, { reel: 3, row: 2 }, { reel: 4, row: 1 }], active: true },
    { id: 7, name: 'Line 7', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 2 }, { reel: 2, row: 1 }, { reel: 3, row: 0 }, { reel: 4, row: 1 }], active: true },

    // Diagonal lines
    { id: 8, name: 'Line 8', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 1 }, { reel: 2, row: 2 }, { reel: 3, row: 2 }, { reel: 4, row: 2 }], active: true },
    { id: 9, name: 'Line 9', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 1 }, { reel: 2, row: 0 }, { reel: 3, row: 0 }, { reel: 4, row: 0 }], active: true },

    // More complex patterns
    { id: 10, name: 'Line 10', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 0 }, { reel: 2, row: 0 }, { reel: 3, row: 1 }, { reel: 4, row: 2 }], active: true },
    { id: 11, name: 'Line 11', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 2 }, { reel: 2, row: 2 }, { reel: 3, row: 1 }, { reel: 4, row: 0 }], active: true },
    { id: 12, name: 'Line 12', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 0 }, { reel: 2, row: 1 }, { reel: 3, row: 2 }, { reel: 4, row: 2 }], active: true },
    { id: 13, name: 'Line 13', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 2 }, { reel: 2, row: 1 }, { reel: 3, row: 0 }, { reel: 4, row: 0 }], active: true },
    { id: 14, name: 'Line 14', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 1 }, { reel: 2, row: 1 }, { reel: 3, row: 1 }, { reel: 4, row: 2 }], active: true },
    { id: 15, name: 'Line 15', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 1 }, { reel: 2, row: 1 }, { reel: 3, row: 1 }, { reel: 4, row: 0 }], active: true },

    // W and M patterns
    { id: 16, name: 'Line 16', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 2 }, { reel: 2, row: 0 }, { reel: 3, row: 2 }, { reel: 4, row: 0 }], active: true },
    { id: 17, name: 'Line 17', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 0 }, { reel: 2, row: 2 }, { reel: 3, row: 0 }, { reel: 4, row: 2 }], active: true },

    // Additional patterns
    { id: 18, name: 'Line 18', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 1 }, { reel: 2, row: 0 }, { reel: 3, row: 1 }, { reel: 4, row: 1 }], active: true },
    { id: 19, name: 'Line 19', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 1 }, { reel: 2, row: 2 }, { reel: 3, row: 1 }, { reel: 4, row: 1 }], active: true },
    { id: 20, name: 'Line 20', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 2 }, { reel: 2, row: 1 }, { reel: 3, row: 2 }, { reel: 4, row: 0 }], active: true },
    { id: 21, name: 'Line 21', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 0 }, { reel: 2, row: 1 }, { reel: 3, row: 0 }, { reel: 4, row: 2 }], active: true },
    { id: 22, name: 'Line 22', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 0 }, { reel: 2, row: 2 }, { reel: 3, row: 0 }, { reel: 4, row: 1 }], active: true },
    { id: 23, name: 'Line 23', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 2 }, { reel: 2, row: 0 }, { reel: 3, row: 2 }, { reel: 4, row: 1 }], active: true },
    { id: 24, name: 'Line 24', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 0 }, { reel: 2, row: 2 }, { reel: 3, row: 0 }, { reel: 4, row: 0 }], active: true },
    { id: 25, name: 'Line 25', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 2 }, { reel: 2, row: 0 }, { reel: 3, row: 2 }, { reel: 4, row: 2 }], active: true }
  ],

  // RTP and Volatility Configuration
  rtp: {
    targetRTP: 96.0, // Industry standard
    volatility: 'medium',
    hitFrequency: 25.5, // 25.5% of spins result in wins
    maxWinMultiplier: 1000, // Maximum win is 1000x bet
    adjustmentWindow: 1000, // Adjust RTP based on last 1000 spins
    toleranceRange: 2.0 // ±2% tolerance from target RTP
  },

  // Win Evaluation Configuration
  evaluation: {
    payMechanism: 'paylines',
    enableSpecialFeatures: true,
    winCap: 10000 // Maximum win cap (10,000x bet)
  },

  // Betting Configuration
  betting: {
    minBet: 0.01,
    maxBet: 100.00,
    defaultBet: 1.00,
    maxLines: 25
  },

  // Feature Configuration
  features: {
    autoplay: true,
    turboMode: true,
    maxWinCap: 250000, // Maximum win cap in currency units
    bonusGames: true,
    freeSpins: true
  }
};

/**
 * High volatility configuration for players seeking bigger wins
 */
export const HIGH_VOLATILITY_CONFIG: GameConfiguration = {
  ...STANDARD_5X3_CONFIG,
  rtp: {
    ...STANDARD_5X3_CONFIG.rtp,
    volatility: 'high',
    hitFrequency: 15.0, // Lower hit frequency
    maxWinMultiplier: 5000 // Higher maximum wins
  },
  symbols: STANDARD_5X3_CONFIG.symbols.map(symbol => ({
    ...symbol,
    // Increase payouts for high volatility
    payouts: Object.fromEntries(
      Object.entries(symbol.payouts).map(([count, payout]) => [count, Math.round(payout * 1.5)])
    )
  }))
};

/**
 * Low volatility configuration for casual players
 */
export const LOW_VOLATILITY_CONFIG: GameConfiguration = {
  ...STANDARD_5X3_CONFIG,
  rtp: {
    ...STANDARD_5X3_CONFIG.rtp,
    volatility: 'low',
    hitFrequency: 35.0, // Higher hit frequency
    maxWinMultiplier: 200 // Lower maximum wins
  },
  symbols: STANDARD_5X3_CONFIG.symbols.map(symbol => ({
    ...symbol,
    // Decrease payouts but increase weights for low volatility
    payouts: Object.fromEntries(
      Object.entries(symbol.payouts).map(([count, payout]) => [count, Math.round(payout * 0.7)])
    ),
    weight: Math.round(symbol.weight * 1.2) // Increase appearance frequency
  }))
};

/**
 * 3x3 Classic slot configuration
 */
export const CLASSIC_3X3_CONFIG: GameConfiguration = {
  reels: 3,
  rows: 3,

  symbols: [
    {
      id: 'seven',
      name: 'Lucky Seven',
      type: 'high',
      weight: 5,
      payouts: { 3: 100 }
    },
    {
      id: 'bar',
      name: 'Bar',
      type: 'medium',
      weight: 15,
      payouts: { 3: 25 }
    },
    {
      id: 'cherry',
      name: 'Cherry',
      type: 'low',
      weight: 30,
      payouts: { 2: 2, 3: 10 }
    },
    {
      id: 'lemon',
      name: 'Lemon',
      type: 'low',
      weight: 35,
      payouts: { 3: 5 }
    },
    {
      id: 'orange',
      name: 'Orange',
      type: 'low',
      weight: 40,
      payouts: { 3: 3 }
    }
  ],

  paylines: [
    { id: 1, name: 'Center Line', positions: [{ reel: 0, row: 1 }, { reel: 1, row: 1 }, { reel: 2, row: 1 }], active: true },
    { id: 2, name: 'Top Line', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 0 }, { reel: 2, row: 0 }], active: true },
    { id: 3, name: 'Bottom Line', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 2 }, { reel: 2, row: 2 }], active: true },
    { id: 4, name: 'Diagonal Down', positions: [{ reel: 0, row: 0 }, { reel: 1, row: 1 }, { reel: 2, row: 2 }], active: true },
    { id: 5, name: 'Diagonal Up', positions: [{ reel: 0, row: 2 }, { reel: 1, row: 1 }, { reel: 2, row: 0 }], active: true }
  ],

  rtp: {
    targetRTP: 95.0, // Slightly lower for classic feel
    volatility: 'medium',
    hitFrequency: 20.0,
    maxWinMultiplier: 500,
    adjustmentWindow: 500,
    toleranceRange: 3.0
  },

  evaluation: {
    payMechanism: 'paylines',
    enableSpecialFeatures: false // Classic slots don't have modern features
  },

  betting: {
    minBet: 0.05,
    maxBet: 25.00,
    defaultBet: 1.00,
    maxLines: 5
  },

  features: {
    autoplay: false,
    turboMode: false,
    maxWinCap: 10000,
    bonusGames: false,
    freeSpins: false
  }
};

/**
 * Ways-to-win configuration (243 ways)
 */
export const WAYS_TO_WIN_CONFIG: GameConfiguration = {
  ...STANDARD_5X3_CONFIG,
  evaluation: {
    payMechanism: 'ways-to-win',
    enableSpecialFeatures: true
  },
  betting: {
    ...STANDARD_5X3_CONFIG.betting,
    maxLines: 1 // Ways-to-win uses a single bet multiplier
  }
};

/**
 * Export all configurations
 */
export const INDUSTRY_CONFIGS = {
  STANDARD_5X3: STANDARD_5X3_CONFIG,
  HIGH_VOLATILITY: HIGH_VOLATILITY_CONFIG,
  LOW_VOLATILITY: LOW_VOLATILITY_CONFIG,
  CLASSIC_3X3: CLASSIC_3X3_CONFIG,
  WAYS_TO_WIN: WAYS_TO_WIN_CONFIG
};

/**
 * Get configuration by name
 */
export function getConfig(name: keyof typeof INDUSTRY_CONFIGS): GameConfiguration {
  return INDUSTRY_CONFIGS[name];
}

/**
 * Create custom configuration based on a base config
 */
export function createCustomConfig(
  baseConfig: GameConfiguration,
  overrides: Partial<GameConfiguration>
): GameConfiguration {
  return {
    ...baseConfig,
    ...overrides,
    symbols: overrides.symbols || baseConfig.symbols,
    paylines: overrides.paylines || baseConfig.paylines,
    rtp: { ...baseConfig.rtp, ...overrides.rtp },
    evaluation: { ...baseConfig.evaluation, ...overrides.evaluation },
    betting: { ...baseConfig.betting, ...overrides.betting },
    features: { ...baseConfig.features, ...overrides.features }
  };
}
