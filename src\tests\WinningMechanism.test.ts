/**
 * Comprehensive Tests for Industry-Standard Winning Mechanism
 * Tests RTP, volatility, bonus features, and compliance
 */

import { WinningMechanism } from '../engine/core/WinningMechanism';
import { STANDARD_5X3_CONFIG } from '../engine/config/industryStandardConfig';

describe('WinningMechanism', () => {
  let winningMechanism: WinningMechanism;

  beforeEach(() => {
    winningMechanism = new WinningMechanism(STANDARD_5X3_CONFIG, true);
    winningMechanism.setBalance(10000); // Start with $100.00
  });

  describe('Basic Functionality', () => {
    test('should initialize correctly', () => {
      expect(winningMechanism).toBeDefined();
      expect(winningMechanism.getBalance()).toBe(10000);
      expect(winningMechanism.isSpinning()).toBe(false);
    });

    test('should execute a basic spin', async () => {
      const spinRequest = {
        bet: 100, // $1.00
        lines: 25,
        turbo: false
      };

      const result = await winningMechanism.spin(spinRequest);

      expect(result).toBeDefined();
      expect(result.grid).toHaveLength(3); // 3 rows
      expect(result.grid[0]).toHaveLength(5); // 5 reels
      expect(result.totalPayout).toBeGreaterThanOrEqual(0);
      expect(result.newBalance).toBeLessThanOrEqual(10000); // Balance should decrease by at least the bet
    });

    test('should reject spin with insufficient balance', async () => {
      winningMechanism.setBalance(50); // $0.50

      const spinRequest = {
        bet: 100, // $1.00
        lines: 25,
        turbo: false
      };

      await expect(winningMechanism.spin(spinRequest)).rejects.toThrow('Insufficient balance');
    });
  });

  describe('RTP Testing', () => {
    test('should maintain target RTP over many spins', async () => {
      const targetRTP = STANDARD_5X3_CONFIG.rtp.targetRTP;
      const spins = 1000;
      const bet = 100; // $1.00
      const lines = 25;

      let totalWagered = 0;
      let totalPaidOut = 0;

      console.log(`🧪 Testing RTP over ${spins} spins...`);

      for (let i = 0; i < spins; i++) {
        // Reset balance if getting low
        if (winningMechanism.getBalance() < bet * lines) {
          winningMechanism.setBalance(10000);
        }

        const result = await winningMechanism.spin({ bet, lines, turbo: false });
        totalWagered += bet * lines;
        totalPaidOut += result.totalPayout;

        // Log progress every 100 spins
        if ((i + 1) % 100 === 0) {
          const currentRTP = (totalPaidOut / totalWagered) * 100;
          console.log(`Spin ${i + 1}: RTP = ${currentRTP.toFixed(2)}%`);
        }
      }

      const actualRTP = (totalPaidOut / totalWagered) * 100;
      const rtpDeviation = Math.abs(actualRTP - targetRTP);

      console.log(`📊 Final Results:`);
      console.log(`Target RTP: ${targetRTP}%`);
      console.log(`Actual RTP: ${actualRTP.toFixed(2)}%`);
      console.log(`Deviation: ${rtpDeviation.toFixed(2)}%`);
      console.log(`Total Wagered: $${(totalWagered / 100).toFixed(2)}`);
      console.log(`Total Paid Out: $${(totalPaidOut / 100).toFixed(2)}`);

      // RTP should be within 5% of target for 1000 spins
      expect(rtpDeviation).toBeLessThan(5);
    }, 30000); // 30 second timeout for this test
  });

  describe('Bonus Features', () => {
    test('should trigger bonus features', async () => {
      const spins = 500;
      let bonusCount = 0;
      let freeSpinsCount = 0;

      console.log(`🎁 Testing bonus features over ${spins} spins...`);

      for (let i = 0; i < spins; i++) {
        if (winningMechanism.getBalance() < 2500) {
          winningMechanism.setBalance(10000);
        }

        const result = await winningMechanism.spin({ bet: 100, lines: 25, turbo: false });
        
        if (result.bonusTriggered) {
          bonusCount++;
        }
        if (result.freeSpinsTriggered) {
          freeSpinsCount++;
        }
      }

      const bonusFrequency = (bonusCount / spins) * 100;
      const freeSpinsFrequency = (freeSpinsCount / spins) * 100;

      console.log(`📊 Bonus Results:`);
      console.log(`Bonus triggers: ${bonusCount} (${bonusFrequency.toFixed(2)}%)`);
      console.log(`Free spins triggers: ${freeSpinsCount} (${freeSpinsFrequency.toFixed(2)}%)`);

      // Bonus features should trigger at reasonable frequency (1-10%)
      expect(bonusFrequency).toBeGreaterThan(0.5);
      expect(bonusFrequency).toBeLessThan(10);
    }, 20000);
  });

  describe('Audit System', () => {
    test('should log spins correctly', async () => {
      const auditSystem = winningMechanism.getAuditSystem();
      const initialStats = auditSystem.getSessionStatistics();

      await winningMechanism.spin({ bet: 100, lines: 25, turbo: false });

      const finalStats = auditSystem.getSessionStatistics();

      expect(finalStats.totalSpins).toBe(initialStats.totalSpins + 1);
      expect(finalStats.totalWagered).toBe(initialStats.totalWagered + 2500);
    });

    test('should generate compliance report', async () => {
      // Execute a few spins first
      for (let i = 0; i < 10; i++) {
        await winningMechanism.spin({ bet: 100, lines: 25, turbo: false });
      }

      const report = winningMechanism.generateComplianceReport();

      expect(report).toBeDefined();
      expect(report.totalSpins).toBe(10);
      expect(report.complianceStatus).toMatch(/PASS|WARNING|FAIL/);
      expect(report.actualRTP).toBeGreaterThanOrEqual(0);
    });

    test('should verify audit log integrity', async () => {
      const auditSystem = winningMechanism.getAuditSystem();

      // Execute some spins
      for (let i = 0; i < 5; i++) {
        await winningMechanism.spin({ bet: 100, lines: 25, turbo: false });
      }

      const integrity = auditSystem.verifyLogIntegrity();

      expect(integrity.valid).toBe(true);
      expect(integrity.corruptedEntries).toHaveLength(0);
      expect(integrity.totalEntries).toBeGreaterThan(0);
    });
  });

  describe('Win Distribution', () => {
    test('should have proper win distribution', async () => {
      const spins = 500;
      const winCounts = {
        noWin: 0,
        smallWin: 0,
        mediumWin: 0,
        bigWin: 0,
        megaWin: 0
      };

      console.log(`📈 Testing win distribution over ${spins} spins...`);

      for (let i = 0; i < spins; i++) {
        if (winningMechanism.getBalance() < 2500) {
          winningMechanism.setBalance(10000);
        }

        const result = await winningMechanism.spin({ bet: 100, lines: 25, turbo: false });
        const winMultiplier = result.totalPayout / 2500; // bet * lines

        if (winMultiplier === 0) {
          winCounts.noWin++;
        } else if (winMultiplier < 2) {
          winCounts.smallWin++;
        } else if (winMultiplier < 10) {
          winCounts.mediumWin++;
        } else if (winMultiplier < 50) {
          winCounts.bigWin++;
        } else {
          winCounts.megaWin++;
        }
      }

      const hitFrequency = ((spins - winCounts.noWin) / spins) * 100;

      console.log(`📊 Win Distribution:`);
      console.log(`No Win: ${winCounts.noWin} (${(winCounts.noWin/spins*100).toFixed(1)}%)`);
      console.log(`Small Win: ${winCounts.smallWin} (${(winCounts.smallWin/spins*100).toFixed(1)}%)`);
      console.log(`Medium Win: ${winCounts.mediumWin} (${(winCounts.mediumWin/spins*100).toFixed(1)}%)`);
      console.log(`Big Win: ${winCounts.bigWin} (${(winCounts.bigWin/spins*100).toFixed(1)}%)`);
      console.log(`Mega Win: ${winCounts.megaWin} (${(winCounts.megaWin/spins*100).toFixed(1)}%)`);
      console.log(`Hit Frequency: ${hitFrequency.toFixed(2)}%`);

      // Hit frequency should be reasonable (15-35% is typical)
      expect(hitFrequency).toBeGreaterThan(10);
      expect(hitFrequency).toBeLessThan(50);

      // Should have some wins
      expect(winCounts.smallWin + winCounts.mediumWin + winCounts.bigWin).toBeGreaterThan(0);
    }, 25000);
  });

  describe('Performance', () => {
    test('should execute spins efficiently', async () => {
      const spins = 100;
      const startTime = Date.now();

      for (let i = 0; i < spins; i++) {
        if (winningMechanism.getBalance() < 2500) {
          winningMechanism.setBalance(10000);
        }
        await winningMechanism.spin({ bet: 100, lines: 25, turbo: false });
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;
      const avgTimePerSpin = totalTime / spins;

      console.log(`⚡ Performance Results:`);
      console.log(`${spins} spins in ${totalTime}ms`);
      console.log(`Average: ${avgTimePerSpin.toFixed(2)}ms per spin`);

      // Each spin should take less than 100ms on average
      expect(avgTimePerSpin).toBeLessThan(100);
    });
  });

  describe('Configuration', () => {
    test('should export game data correctly', () => {
      const gameData = winningMechanism.exportAuditData();

      expect(gameData).toBeDefined();
      expect(gameData.gameConfig).toBeDefined();
      expect(gameData.gameState).toBeDefined();
      expect(gameData.rtpData).toBeDefined();
      expect(gameData.bonusFeatureData).toBeDefined();
      expect(gameData.auditData).toBeDefined();
    });

    test('should reset session correctly', () => {
      // Execute some spins first
      winningMechanism.spin({ bet: 100, lines: 25, turbo: false });

      const beforeReset = winningMechanism.getAuditSystem().getSessionStatistics();
      expect(beforeReset.totalSpins).toBeGreaterThan(0);

      winningMechanism.resetSession();

      const afterReset = winningMechanism.getAuditSystem().getSessionStatistics();
      expect(afterReset.totalSpins).toBe(0);
    });
  });
});
